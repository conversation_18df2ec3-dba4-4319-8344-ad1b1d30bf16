#!/usr/bin/env python
import sys
import os
sys.path.insert(0, os.path.abspath('.'))

# Test imports step by step
try:
    import pandas as pd
    print("✅ pandas")
    
    import mysql.connector
    print("✅ mysql.connector")
    
    from contextlib import contextmanager
    print("✅ contextmanager")
    
    from helper.setup_logger import setup_logger
    print("✅ setup_logger")
    
    from db.db_setup import get_db_connection, CONN
    print("✅ db_setup")
    
    from db.safe_db_utils import safe_read_sql
    print("✅ safe_db_utils")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()