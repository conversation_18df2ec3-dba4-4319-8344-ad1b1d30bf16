#!/usr/bin/env python3
"""
Test script to identify import issues
"""

import sys
import traceback

def test_import(module_name, import_statement):
    """Test a specific import and report results"""
    print(f"Testing {module_name}...")
    try:
        exec(import_statement)
        print(f"✅ {module_name} imported successfully")
        return True
    except Exception as e:
        print(f"❌ {module_name} import failed: {e}")
        traceback.print_exc()
        return False

def main():
    print("Testing all imports from app.py...")
    
    # Test basic imports
    test_import("streamlit", "import streamlit as st")
    test_import("helper.setup_logger", "from helper.setup_logger import setup_logger")
    test_import("base64", "import base64")
    
    # Test UI components
    test_import("login", "from frontend.ui_components.login import initialize_session_state")
    test_import("dashboard_controls", "from frontend.ui_components.dashboard_controls import create_client_plant_filters")
    
    # Test display functions
    test_import("summary_display", "from frontend.display_plots.summary_display import display_generation_vs_consumption")
    test_import("tod_display", "from frontend.display_plots.tod_display import display_monthly_tod_before_banking")
    test_import("power_cost_display", "from frontend.display_plots.power_cost_display import display_power_cost_analysis")
    
    # Test data management
    test_import("db_data_manager", "from backend.data.db_data_manager import load_client_data")
    test_import("db_setup", "from db.db_setup import CONN")
    
    print("\nImport testing complete!")

if __name__ == "__main__":
    main()