2025-07-10 16:02:32,350 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for E6HT209
2025-07-10 16:02:32,353 - ERROR - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:762 - <PERSON><PERSON>r creating single unit timeseries for E6HT209: 0
2025-07-10 16:02:32,357 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:933 - Creating single unit savings trend for E6HT209
2025-07-10 16:02:32,358 - ERROR - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:982 - Error creating single unit savings trend for E6HT209: 0
2025-07-10 16:05:31,763 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for E6HT209
2025-07-10 16:05:31,815 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:939 - Successfully created interactive single unit timeseries for E6HT209
2025-07-10 16:05:31,819 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:957 - Creating single unit savings trend for E6HT209
2025-07-10 16:05:31,832 - ERROR - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1029 - Error creating single unit savings trend for E6HT209: 0
2025-07-10 16:05:31,832 - ERROR - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1030 - Exception details - Type: <class 'KeyError'>, Args: (0,)
2025-07-10 16:06:15,337 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:06:15,399 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:939 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:06:15,405 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:957 - Creating single unit savings trend for C2HT-136
2025-07-10 16:06:15,455 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1255 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:08:02,641 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:08:02,656 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:08:02,666 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:08:02,681 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:09:25,012 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-10 16:09:25,045 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-10 16:09:25,048 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-10 16:09:25,085 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-10 16:13:25,209 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-10 16:13:25,252 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-10 16:13:25,255 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-10 16:13:25,281 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-10 16:15:51,557 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-10 16:15:51,585 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-10 16:15:51,588 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-10 16:15:51,632 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-10 16:18:30,319 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-10 16:18:30,395 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-10 16:18:30,414 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-10 16:18:30,489 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-10 16:18:41,470 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-10 16:18:41,497 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-10 16:18:41,502 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-10 16:18:41,544 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-10 16:23:51,809 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:23:51,828 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:23:51,833 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:23:51,864 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:26:27,202 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:26:27,221 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:26:27,224 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:26:27,236 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:28:56,600 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:28:56,612 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:28:56,617 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:28:56,632 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:29:24,288 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:29:24,296 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:29:24,299 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:29:24,313 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:29:55,395 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:29:55,411 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:29:55,421 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:29:55,424 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:32:56,166 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:32:56,194 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:32:56,199 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:32:56,215 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:34:39,189 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:34:39,206 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:34:39,214 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:34:39,229 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:36:03,001 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:36:03,001 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:36:03,015 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:36:03,034 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:38:30,319 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:38:30,328 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:38:30,332 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:38:30,345 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:39:58,679 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:39:58,701 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:39:58,707 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:39:58,736 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:40:24,916 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:40:24,926 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:40:24,932 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:40:24,947 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:41:11,352 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:41:11,352 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:41:11,365 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:41:11,368 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:41:32,770 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:41:32,784 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:41:32,789 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:41:32,804 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-10 16:41:47,252 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:716 - Creating single unit cost timeseries for C2HT-136
2025-07-10 16:41:47,254 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:935 - Successfully created interactive single unit timeseries for C2HT-136
2025-07-10 16:41:47,263 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:953 - Creating single unit savings trend for C2HT-136
2025-07-10 16:41:47,278 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:1247 - Successfully created interactive single unit savings trend for C2HT-136
2025-07-11 11:12:34,325 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 11:12:34,387 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 11:12:34,387 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 11:12:34,577 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 11:16:36,486 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 11:16:36,534 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 11:16:36,550 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 11:16:36,629 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 11:17:50,092 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 11:17:50,127 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 11:17:50,128 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 11:17:50,164 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 11:28:51,944 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 11:28:51,963 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 11:28:51,970 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 11:28:51,991 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 11:30:14,671 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 11:30:14,685 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 11:30:14,698 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 11:30:14,734 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 11:59:01,704 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 11:59:01,727 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 11:59:01,740 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 11:59:01,777 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:02:31,437 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:02:31,513 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:02:31,521 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:02:31,666 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:09:41,221 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:09:41,269 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:09:41,269 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:09:41,341 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:17:37,158 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:17:37,243 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:17:37,250 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:17:37,315 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:20:21,965 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:20:21,995 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:20:22,000 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:20:22,033 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:21:51,905 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:21:51,926 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:21:51,937 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:21:51,957 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:22:10,255 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:22:10,279 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:22:10,282 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:22:10,313 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:25:14,286 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:25:14,325 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:25:14,325 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:25:14,397 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:28:46,151 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:28:46,186 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:28:46,189 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:28:46,220 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:37:20,972 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:37:20,997 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:37:21,002 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:37:21,047 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:41:39,062 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:41:39,096 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:41:39,097 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:41:39,152 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:42:25,809 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:42:25,847 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:42:25,851 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:42:25,902 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:47:29,000 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:47:29,027 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:47:29,030 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:47:29,068 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:49:12,871 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:49:12,906 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:49:12,906 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:49:12,985 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
2025-07-11 12:51:37,086 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:271 - Creating unit-wise grid vs actual cost bar chart for Kids Clinic India Limited
2025-07-11 12:51:37,111 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:482 - Successfully created interactive unit-wise bar chart
2025-07-11 12:51:37,116 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:499 - Creating unit-wise monthly savings heatmap for Kids Clinic India Limited
2025-07-11 12:51:37,152 - INFO - unit_wise_power_cost_calculations - unit_wise_power_cost_calculations.py:698 - Successfully created interactive unit-wise heatmap
