"""
Test minimal version of unit_wise_power_cost_calculations to isolate the segfault
"""

print("Starting imports...")

import pandas as pd
print("pandas OK")

import matplotlib.pyplot as plt
print("matplotlib OK")

import numpy as np
print("numpy OK")

import seaborn as sns
print("seaborn OK")

from matplotlib.ticker import FuncFormatter
print("FuncFormatter OK")

import plotly.graph_objects as go
print("plotly.graph_objects OK")

import plotly.express as px
print("plotly.express OK")

from db.safe_db_utils import safe_read_sql
print("safe_read_sql OK")

from helper.setup_logger import setup_logger
print("setup_logger OK")

logging = setup_logger("test_unit_wise", "test_unit_wise.log")
print("logger setup OK")

print("All imports successful!")

# Test a simple function
def test_function():
    return "Test function works"

print(test_function())
