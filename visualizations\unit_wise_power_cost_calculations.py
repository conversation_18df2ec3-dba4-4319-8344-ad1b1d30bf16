import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.ticker import FuncFormatter
import plotly.graph_objects as go
import plotly.express as px
from db.safe_db_utils import safe_read_sql
from helper.setup_logger import setup_logger

logging = setup_logger("unit_wise_power_cost_calculations", "unit_wise_power_cost_calculations.log")


def fetch_unitwise_monthly_data(
    conn,
    client_name: str = None
) -> pd.DataFrame:
    """
    Fetch monthly aggregated data for consumption and settled amounts grouped by cons_unit.

    Args:
        conn: MySQL connection object
        client_name (str, optional): Filter by client name

    Returns:
        pd.DataFrame: DataFrame with unit-wise month-wise consumption and settlement data
    """

    # --- Fetch unit-wise consumption and settlement data ---
    query = """
        SELECT
            cons_unit,
            date,
            consumption,
            allocated_generation,
            settled
        FROM
            settlement_data
        WHERE
            date IS NOT NULL
            {client_filter}
        ORDER BY
            cons_unit, date;
    """

    # Prepare query and params
    params = ()
    if client_name:
        query = query.format(client_filter="AND client_name = %s")
        params = (client_name,)
    else:
        query = query.format(client_filter="")

    # Read data
    df = safe_read_sql(query, conn, params)
    if df.empty:
        logging.warning("No data found")
        return pd.DataFrame()

    # Convert date and create month column
    df['date'] = pd.to_datetime(df['date'])
    df['month'] = df['date'].dt.to_period('M').astype(str)

    # Group by cons_unit and month
    df_monthly = df.groupby(['cons_unit', 'month'], as_index=False).agg({
        'consumption': 'sum',
        'allocated_generation': 'sum',
        'settled': 'sum'
    }).rename(columns={
        'consumption': 'total_consumption_sum',
        'allocated_generation': 'total_allocated_generation',
        'settled': 'total_settled'
    })

    return df_monthly


def calculate_unitwise_monthly_power_costs(df: pd.DataFrame, grid_rate_per_kwh: float = 4.0, renewable_rate_per_kwh: float = 2.0) -> pd.DataFrame:
    """
    Calculate unit-wise monthly cost metrics and return in clean format with renamed columns.

    Args:
        df (pd.DataFrame): DataFrame with columns:
            ['cons_unit', 'month', 'total_consumption_sum', 'total_allocated_generation', 'total_settled']
        grid_rate_per_kwh (float): Grid rate in ₹ per kWh
        renewable_rate_per_kwh (float): Renewable rate in ₹ per kWh

    Returns:
        pd.DataFrame: Formatted with required columns
    """
    if df.empty:
        return pd.DataFrame()

    df = df.copy()

    # Fill NaNs to ensure safe arithmetic
    for col in ['total_consumption_sum', 'total_allocated_generation', 'total_settled']:
        df[col] = df[col].fillna(0)

    # Net consumption = what still had to be bought from the grid after settlement
    df['grid_consumption'] = (df['total_consumption_sum'] - df['total_settled']).clip(lower=0)

    # Costs
    df['grid_cost'] = df['total_consumption_sum'] * grid_rate_per_kwh
    df['renewable_cost'] = df['total_settled'] * renewable_rate_per_kwh
    df['actual_cost'] = df['grid_consumption'] * grid_rate_per_kwh + df['renewable_cost']
    df['savings'] = df['grid_cost'] - df['actual_cost']
    df['savings_percentage'] = df.apply(
        lambda row: round(row['savings'] / row['grid_cost'] * 100, 2) if row['grid_cost'] > 0 else 0,
        axis=1
    )

    # Final formatting
    df_final = df[[
        'cons_unit',
        'month',
        'grid_cost',
        'actual_cost',
        'savings',
        'total_settled',
        'savings_percentage'
    ]].rename(columns={
        'cons_unit': 'Unit',
        'month': 'Month',
        'grid_cost': 'Grid Cost (₹)',
        'actual_cost': 'Actual Cost (₹)',
        'savings': 'Savings (₹)',
        'total_settled': 'Energy Offset (kWh)',
        'savings_percentage': 'Savings (%)'
    })

    return df_final


def create_unitwise_monthly_bill_table(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create a pivot table showing unit-wise monthly bills with grid power cost and after adjustment.
    
    Args:
        df (pd.DataFrame): Unit-wise monthly cost data
    
    Returns:
        pd.DataFrame: Pivot table with units as rows and months as columns
    """
    if df.empty:
        return pd.DataFrame()
    
    # Create pivot table for Grid Cost
    grid_cost_pivot = df.pivot_table(
        index='Unit',
        columns='Month',
        values='Grid Cost (₹)',
        fill_value=0,
        aggfunc='sum'
    )
    
    # Create pivot table for Actual Cost (after adjustment)
    actual_cost_pivot = df.pivot_table(
        index='Unit',
        columns='Month',
        values='Actual Cost (₹)',
        fill_value=0,
        aggfunc='sum'
    )
    
    # Combine both tables with multi-level columns
    combined_table = pd.concat([
        grid_cost_pivot.add_suffix(' (Grid)'),
        actual_cost_pivot.add_suffix(' (Adjusted)')
    ], axis=1)
    
    # Sort columns chronologically
    columns = sorted(combined_table.columns, key=lambda x: (x.split(' ')[0], x.split(' ')[1]))
    combined_table = combined_table[columns]
    
    # Add totals row
    totals = combined_table.sum()
    combined_table.loc['TOTAL'] = totals
    
    return combined_table


def summarize_unitwise_costs_table(df: pd.DataFrame) -> pd.DataFrame:
    """
    Summarize unit-wise cost metrics from monthly-level DataFrame with month information.

    Args:
        df (pd.DataFrame): Unit-wise monthly cost data

    Returns:
        pd.DataFrame: Summary table with unit-wise and month-wise data
    """
    if df.empty:
        return pd.DataFrame()

    # Keep the original data with month information
    summary = df.copy()

    # Sort by Unit and Month for better readability
    summary = summary.sort_values(['Unit', 'Month']).reset_index(drop=True)

    # Add a summary row for each unit
    unit_summaries = []
    for unit in summary['Unit'].unique():
        if unit == 'TOTAL':  # Skip if TOTAL already exists
            continue

        unit_data = summary[summary['Unit'] == unit]
        unit_total = {
            'Unit': f"{unit} - TOTAL",
            'Month': 'ALL MONTHS',
            'Grid Cost (₹)': unit_data['Grid Cost (₹)'].sum(),
            'Actual Cost (₹)': unit_data['Actual Cost (₹)'].sum(),
            'Savings (₹)': unit_data['Savings (₹)'].sum(),
            'Energy Offset (kWh)': unit_data['Energy Offset (kWh)'].sum(),
            'Savings (%)': round(unit_data['Savings (₹)'].sum() / unit_data['Grid Cost (₹)'].sum() * 100, 2)
            if unit_data['Grid Cost (₹)'].sum() > 0 else 0
        }
        unit_summaries.append(unit_total)

    # Add unit summaries to the main dataframe
    if unit_summaries:
        summary = pd.concat([summary, pd.DataFrame(unit_summaries)], ignore_index=True)

    # Add overall total row
    total_row = {
        'Unit': 'GRAND TOTAL',
        'Month': 'ALL MONTHS',
        'Grid Cost (₹)': summary[summary['Unit'] != 'GRAND TOTAL']['Grid Cost (₹)'].sum(),
        'Actual Cost (₹)': summary[summary['Unit'] != 'GRAND TOTAL']['Actual Cost (₹)'].sum(),
        'Savings (₹)': summary[summary['Unit'] != 'GRAND TOTAL']['Savings (₹)'].sum(),
        'Energy Offset (kWh)': summary[summary['Unit'] != 'GRAND TOTAL']['Energy Offset (kWh)'].sum(),
        'Savings (%)': round(summary[summary['Unit'] != 'GRAND TOTAL']['Savings (₹)'].sum() /
                           summary[summary['Unit'] != 'GRAND TOTAL']['Grid Cost (₹)'].sum() * 100, 2)
        if summary[summary['Unit'] != 'GRAND TOTAL']['Grid Cost (₹)'].sum() > 0 else 0
    }

    summary = pd.concat([summary, pd.DataFrame([total_row])], ignore_index=True)

    return summary


def format_rupees_lakhs(x, _):
    """Format rupees with lakhs notation for large values"""
    try:
        if x >= 1e5:
            return f"₹{x / 1e5:.1f}L"
        else:
            return f"₹{x:,.0f}"
    except (TypeError, ValueError) as e:
        logging.warning(f"Error formatting rupees value {x}: {str(e)}")
        return str(x) if x is not None else '₹0'
    except Exception as e:
        logging.error(f"Unexpected error in format_rupees_lakhs: {str(e)}")
        return '₹0'


def plot_unitwise_grid_vs_actual_cost_bar_chart(df: pd.DataFrame, client_name: str = None, use_interactive: bool = False):
    """
    Create a grouped bar chart showing Grid Cost vs Actual Cost for each unit.
    
    Args:
        df (pd.DataFrame): Unit-wise monthly cost data
        client_name (str): Client name for title
        use_interactive (bool): Whether to use interactive Plotly chart
    
    Returns:
        matplotlib.figure.Figure or plotly.graph_objects.Figure: The chart figure
    """
    try:
        logging.info(f"Creating unit-wise grid vs actual cost bar chart for {client_name}")
        
        if df is None or df.empty:
            logging.warning("No data available for unit-wise bar chart")
            if use_interactive:
                fig = go.Figure()
                fig.add_annotation(
                    text="No unit-wise cost data available",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5, xanchor='center', yanchor='middle',
                    font=dict(size=20, color="red")
                )
                fig.update_layout(
                    title="Unit-wise Grid Cost vs Actual Cost",
                    xaxis=dict(showgrid=False, showticklabels=False),
                    yaxis=dict(showgrid=False, showticklabels=False)
                )
                return fig
            else:
                fig, ax = plt.subplots(figsize=(12, 6))
                ax.text(0.5, 0.5, "No unit-wise cost data available", ha='center', va='center', fontsize=14, color='red')
                ax.set_title("Unit-wise Grid Cost vs Actual Cost", fontsize=14, fontweight='bold')
                return fig
        
        # Aggregate by unit (sum across all months)
        unit_summary = df.groupby('Unit').agg({
            'Grid Cost (₹)': 'sum',
            'Actual Cost (₹)': 'sum',
            'Savings (₹)': 'sum'
        }).reset_index()
        
        # Calculate savings percentage
        unit_summary['Savings (%)'] = unit_summary.apply(
            lambda row: (row['Savings (₹)'] / row['Grid Cost (₹)'] * 100) if row['Grid Cost (₹)'] > 0 else 0,
            axis=1
        )
        
        # Sort by total grid cost for better visualization
        unit_summary = unit_summary.sort_values('Grid Cost (₹)', ascending=False)
        
        if use_interactive:
            return _create_interactive_unitwise_bar_chart(unit_summary, client_name)
        else:
            return _create_static_unitwise_bar_chart(unit_summary, client_name)
            
    except Exception as e:
        logging.error(f"Error creating unit-wise bar chart: {str(e)}")
        if use_interactive:
            fig = go.Figure()
            fig.add_annotation(
                text="Error creating chart - please try again",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(title="Unit-wise Grid Cost vs Actual Cost")
            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.text(0.5, 0.5, "Error creating chart - please try again", ha='center', va='center', fontsize=14, color='red')
            ax.set_title("Unit-wise Grid Cost vs Actual Cost", fontsize=14, fontweight='bold')
            return fig


def _create_static_unitwise_bar_chart(unit_summary: pd.DataFrame, client_name: str):
    """Create static matplotlib version of unit-wise bar chart"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Set up the bar positions
    x = np.arange(len(unit_summary))
    width = 0.35
    
    # Create bars
    grid_bars = ax.bar(x - width/2, unit_summary['Grid Cost (₹)'], width, 
                      label='Grid Cost', color='#E53935', alpha=0.8)
    actual_bars = ax.bar(x + width/2, unit_summary['Actual Cost (₹)'], width, 
                        label='Actual Cost', color='#43A047', alpha=0.8)
    
    # Add value labels on bars
    for i, (grid_cost, actual_cost, savings) in enumerate(zip(
        unit_summary['Grid Cost (₹)'], 
        unit_summary['Actual Cost (₹)'], 
        unit_summary['Savings (₹)']
    )):
        # Grid cost label
        ax.text(x[i] - width/2, grid_cost + max(unit_summary['Grid Cost (₹)']) * 0.01,
                f"₹{grid_cost/1e5:.1f}L", ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        # Actual cost label
        ax.text(x[i] + width/2, actual_cost + max(unit_summary['Grid Cost (₹)']) * 0.01,
                f"₹{actual_cost/1e5:.1f}L", ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        # Savings annotation above the bars
        max_height = max(grid_cost, actual_cost)
        savings_pct = (savings / grid_cost * 100) if grid_cost > 0 else 0
        ax.text(x[i], max_height + max(unit_summary['Grid Cost (₹)']) * 0.05,
                f"₹{savings/1e5:.1f}L\n({savings_pct:.1f}%)", 
                ha='center', va='bottom', fontsize=8, color='green', fontweight='bold')
    
    # Formatting
    ax.set_xlabel('Units', fontsize=12, fontweight='bold')
    ax.set_ylabel('Cost (₹ in Lakhs)', fontsize=12, fontweight='bold')
    title = f"Unit-wise Grid Cost vs Actual Cost"
    if client_name:
        title += f"\n{client_name}"
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(unit_summary['Unit'], rotation=45, ha='right')
    ax.yaxis.set_major_formatter(FuncFormatter(format_rupees_lakhs))
    ax.legend(loc='upper right')
    ax.grid(True, axis='y', alpha=0.3)
    
    plt.tight_layout()
    logging.info("Successfully created static unit-wise bar chart")
    return fig


def _create_interactive_unitwise_bar_chart(unit_summary: pd.DataFrame, client_name: str):
    """Create interactive Plotly version of unit-wise bar chart"""
    
    fig = go.Figure()
    
    # Helper function to format currency
    def format_currency(value):
        if value >= 1e5:
            return f"₹{value / 1e5:.1f}L"
        else:
            return f"₹{value:,.0f}"
    
    # Grid Cost bars
    fig.add_trace(go.Bar(
        x=unit_summary['Unit'],
        y=unit_summary['Grid Cost (₹)'],
        name='Grid Cost',
        marker_color='#E53935',
        text=[format_currency(val) for val in unit_summary['Grid Cost (₹)']],
        textposition='outside',
        hovertemplate='<b>🏢 Unit:</b> %{x}<br>' +
                     '<b>⚡ Grid Cost:</b> %{text}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>'
    ))
    
    # Actual Cost bars
    fig.add_trace(go.Bar(
        x=unit_summary['Unit'],
        y=unit_summary['Actual Cost (₹)'],
        name='Actual Cost',
        marker_color='#43A047',
        text=[format_currency(val) for val in unit_summary['Actual Cost (₹)']],
        textposition='outside',
        hovertemplate='<b>🏢 Unit:</b> %{x}<br>' +
                     '<b>🏦 Actual Cost:</b> %{text}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>'
    ))
    
    # Add savings annotations
    for i, (unit, grid_cost, actual_cost, savings, savings_pct) in enumerate(zip(
        unit_summary['Unit'], 
        unit_summary['Grid Cost (₹)'], 
        unit_summary['Actual Cost (₹)'], 
        unit_summary['Savings (₹)'],
        unit_summary['Savings (%)']
    )):
        max_height = max(grid_cost, actual_cost)
        fig.add_annotation(
            x=unit,
            y=max_height * 1.15,
            text=f"💰 {format_currency(savings)}<br>📊 {savings_pct:.1f}%",
            showarrow=False,
            font=dict(size=10, color='green'),
            bgcolor='rgba(255,255,255,0.8)',
            bordercolor='green',
            borderwidth=1
        )
    
    # Update layout
    title = f"Unit-wise Grid Cost vs Actual Cost"
    if client_name:
        title += f"<br>{client_name}"
    
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Units",
            tickangle=45
        ),
        yaxis=dict(
            title="Cost (₹ in Lakhs)",
            tickformat=',.0f'
        ),
        height=700,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        barmode='group',
        hovermode='x unified'
    )
    
    logging.info("Successfully created interactive unit-wise bar chart")
    return fig


def plot_unitwise_monthly_savings_heatmap(df: pd.DataFrame, client_name: str = None, use_interactive: bool = False):
    """
    Create a heatmap showing monthly savings by unit.
    
    Args:
        df (pd.DataFrame): Unit-wise monthly cost data
        client_name (str): Client name for title
        use_interactive (bool): Whether to use interactive Plotly chart
    
    Returns:
        matplotlib.figure.Figure or plotly.graph_objects.Figure: The heatmap figure
    """
    try:
        logging.info(f"Creating unit-wise monthly savings heatmap for {client_name}")
        
        if df is None or df.empty:
            logging.warning("No data available for unit-wise heatmap")
            if use_interactive:
                fig = go.Figure()
                fig.add_annotation(
                    text="No unit-wise cost data available",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5, xanchor='center', yanchor='middle',
                    font=dict(size=20, color="red")
                )
                fig.update_layout(
                    title="Unit-wise Monthly Savings Heatmap",
                    xaxis=dict(showgrid=False, showticklabels=False),
                    yaxis=dict(showgrid=False, showticklabels=False)
                )
                return fig
            else:
                fig, ax = plt.subplots(figsize=(12, 6))
                ax.text(0.5, 0.5, "No unit-wise cost data available", ha='center', va='center', fontsize=14, color='red')
                ax.set_title("Unit-wise Monthly Savings Heatmap", fontsize=14, fontweight='bold')
                return fig
        
        # Create pivot table for heatmap
        heatmap_data = df.pivot_table(
            index='Unit',
            columns='Month',
            values='Savings (%)',
            fill_value=0,
            aggfunc='sum'
        )
        
        # Sort months chronologically
        try:
            # Convert month strings to datetime for sorting
            month_order = sorted(heatmap_data.columns, key=lambda x: pd.to_datetime(x + '-01'))
            heatmap_data = heatmap_data[month_order]
        except Exception as e:
            logging.warning(f"Could not sort months chronologically: {str(e)}")
            # Keep original order if sorting fails
        
        # Sort units by average savings (descending)
        unit_avg_savings = heatmap_data.mean(axis=1).sort_values(ascending=False)
        heatmap_data = heatmap_data.reindex(unit_avg_savings.index)
        
        if use_interactive:
            return _create_interactive_unitwise_heatmap(heatmap_data, df, client_name)
        else:
            return _create_static_unitwise_heatmap(heatmap_data, client_name)
            
    except Exception as e:
        logging.error(f"Error creating unit-wise heatmap: {str(e)}")
        if use_interactive:
            fig = go.Figure()
            fig.add_annotation(
                text="Error creating heatmap - please try again",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(title="Unit-wise Monthly Savings Heatmap")
            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.text(0.5, 0.5, "Error creating heatmap - please try again", ha='center', va='center', fontsize=14, color='red')
            ax.set_title("Unit-wise Monthly Savings Heatmap", fontsize=14, fontweight='bold')
            return fig


def _create_static_unitwise_heatmap(heatmap_data: pd.DataFrame, client_name: str):
    """Create static matplotlib version of unit-wise heatmap"""
    
    # Dynamic figure size based on data dimensions - increased width for better readability
    fig_width = max(16, len(heatmap_data.columns) * 1.8)
    fig_height = max(10, len(heatmap_data.index) * 0.8)
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    
    # Create the heatmap
    im = ax.imshow(heatmap_data.values, cmap='RdYlGn', aspect='auto', 
                   vmin=0, vmax=heatmap_data.values.max())
    
    # Set ticks and labels with better formatting
    ax.set_xticks(range(len(heatmap_data.columns)))
    ax.set_yticks(range(len(heatmap_data.index)))
    ax.set_xticklabels(heatmap_data.columns, rotation=45, ha='right', fontsize=11, fontweight='bold')
    ax.set_yticklabels(heatmap_data.index, fontsize=11, fontweight='bold')
    
    # Add text annotations
    for i in range(len(heatmap_data.index)):
        for j in range(len(heatmap_data.columns)):
            value = heatmap_data.iloc[i, j]
            text_color = 'white' if value > heatmap_data.values.max() * 0.6 else 'black'
            ax.text(j, i, f'{value:.1f}%', ha='center', va='center', 
                   color=text_color, fontsize=11, fontweight='bold')
    
    # Add colorbar
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('Savings (%)', rotation=270, labelpad=20, fontsize=12)
    
    # Enhanced formatting with better title
    title = f"🔥 Unit-wise Monthly Savings Heatmap"
    if client_name:
        title += f"\n📍 {client_name}"
    ax.set_title(title, fontsize=16, fontweight='bold', pad=25)
    ax.set_xlabel('📅 Month', fontsize=13, fontweight='bold')
    ax.set_ylabel('🏢 Unit', fontsize=13, fontweight='bold')
    
    plt.tight_layout()
    logging.info("Successfully created static unit-wise heatmap")
    return fig


def _create_interactive_unitwise_heatmap(heatmap_data: pd.DataFrame, original_df: pd.DataFrame, client_name: str):
    """Create interactive Plotly version of unit-wise heatmap"""
    
    # Create hover text with additional information
    hover_text = []
    for unit in heatmap_data.index:
        row_hover = []
        for month in heatmap_data.columns:
            # Get additional data for this unit-month combination
            unit_month_data = original_df[
                (original_df['Unit'] == unit) & (original_df['Month'] == month)
            ]
            
            if not unit_month_data.empty:
                row_data = unit_month_data.iloc[0]
                savings_pct = row_data['Savings (%)']
                savings_amount = row_data['Savings (₹)']
                grid_cost = row_data['Grid Cost (₹)']
                actual_cost = row_data['Actual Cost (₹)']
                
                hover_info = (
                    f"<b>🏢 Unit:</b> {unit}<br>"
                    f"<b>📅 Month:</b> {month}<br>"
                    f"<b>💸 Savings:</b> {savings_pct:.1f}%<br>"
                    f"<b>💰 Amount Saved:</b> ₹{savings_amount:,.0f}<br>"
                    f"<b>⚡ Grid Cost:</b> ₹{grid_cost:,.0f}<br>"
                    f"<b>🏦 Actual Cost:</b> ₹{actual_cost:,.0f}"
                )
            else:
                hover_info = f"<b>🏢 Unit:</b> {unit}<br><b>📅 Month:</b> {month}<br><b>💸 Savings:</b> 0.0%"
            
            row_hover.append(hover_info)
        hover_text.append(row_hover)
    
    fig = go.Figure(data=go.Heatmap(
        z=heatmap_data.values,
        x=heatmap_data.columns,
        y=heatmap_data.index,
        colorscale='RdYlGn',
        colorbar=dict(title="Savings (%)"),
        hovertemplate='%{customdata}<extra></extra>',
        customdata=hover_text,
        text=[[f"{val:.1f}%" for val in row] for row in heatmap_data.values],
        texttemplate="%{text}",
        textfont={"size": 12, "color": "white", "family": "Arial Black"},
        zmin=0,
        zmax=heatmap_data.values.max()
    ))
    
    # Update layout with enhanced styling
    title = f"🔥 Unit-wise Monthly Savings Heatmap"
    if client_name:
        title += f"<br><span style='font-size: 14px; color: #7f8c8d;'>📍 {client_name}</span>"
    
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,
            xanchor='center',
            font=dict(size=18, color='#2c3e50', family='Arial Black'),
            pad=dict(t=20, b=20)
        ),
        xaxis=dict(
            title=dict(
                text="📅 Month",
                font=dict(size=14, color='#2c3e50', family='Arial')
            ),
            tickangle=45,
            tickfont=dict(size=12, color='#34495e')
        ),
        yaxis=dict(
            title=dict(
                text="🏢 Unit",
                font=dict(size=14, color='#2c3e50', family='Arial')
            ),
            tickmode='linear',
            tickfont=dict(size=12, color='#34495e')
        ),
        height=max(500, len(heatmap_data.index) * 50),  # Dynamic height based on number of units
        width=max(1200, len(heatmap_data.columns) * 120),  # Dynamic width based on number of months - increased for better readability
        plot_bgcolor='#f8f9fa',
        paper_bgcolor='#ffffff',
        font=dict(family='Arial', size=11, color='#2c3e50'),
        margin=dict(l=80, r=80, t=100, b=60)
    )
    
    logging.info("Successfully created interactive unit-wise heatmap")
    return fig


def plot_single_unit_cost_timeseries(df: pd.DataFrame, unit_name: str, client_name: str = None, use_interactive: bool = False):
    """
    Create a time series line chart showing Grid Cost vs Actual Cost for a single unit.
    
    Args:
        df (pd.DataFrame): Single unit monthly cost data
        unit_name (str): Name of the unit
        client_name (str): Client name for title
        use_interactive (bool): Whether to use interactive Plotly chart
    
    Returns:
        matplotlib.figure.Figure or plotly.graph_objects.Figure: The chart figure
    """
    try:
        logging.info(f"Creating single unit cost timeseries for {unit_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for single unit timeseries - {unit_name}")
            if use_interactive:
                fig = go.Figure()
                fig.add_annotation(
                    text=f"No cost data available for {unit_name}",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5, xanchor='center', yanchor='middle',
                    font=dict(size=20, color="red")
                )
                fig.update_layout(title=f"Cost Trend - {unit_name}")
                return fig
            else:
                fig, ax = plt.subplots(figsize=(12, 6))
                ax.text(0.5, 0.5, f"No cost data available for {unit_name}", 
                       ha='center', va='center', fontsize=14, color='red')
                ax.set_title(f"Cost Trend - {unit_name}", fontsize=14, fontweight='bold')
                return fig
        
        # Sort by month
        df = df.sort_values('Month')
        
        # Validate required columns exist
        required_cols = ['Month', 'Savings (₹)', 'Grid Cost (₹)', 'Actual Cost (₹)']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Check if savings column has any valid data
        if df['Savings (₹)'].isna().all() or df['Savings (₹)'].empty:
            raise ValueError("No valid savings data available")
        
        # Find the month with maximum savings
        max_savings_idx = df['Savings (₹)'].idxmax()
        max_savings_month = df.loc[max_savings_idx, 'Month']
        max_savings_amount = df.loc[max_savings_idx, 'Savings (₹)']
        
        if use_interactive:
            return _create_interactive_single_unit_timeseries(df, unit_name, client_name, max_savings_month, max_savings_amount)
        else:
            return _create_static_single_unit_timeseries(df, unit_name, client_name, max_savings_month, max_savings_amount)
            
    except ZeroDivisionError as e:
        logging.error(f"ZeroDivisionError in single unit timeseries for {unit_name}: {str(e)}")
        error_msg = "Division by zero error - check data integrity"
        if use_interactive:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating chart: {error_msg}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=16, color="red")
            )
            fig.update_layout(title=f"Cost Trend - {unit_name}")
            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.text(0.5, 0.5, f"Error creating chart: {error_msg}", 
                   ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Cost Trend - {unit_name}", fontsize=14, fontweight='bold')
            return fig
    except Exception as e:
        error_msg = str(e) if str(e) else f"Unknown error occurred - Exception type: {type(e).__name__}"
        logging.error(f"Error creating single unit timeseries for {unit_name}: {error_msg}")
        logging.error(f"Exception details - Type: {type(e)}, Args: {e.args}")
        if use_interactive:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating chart: {error_msg}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=16, color="red")
            )
            fig.update_layout(title=f"Cost Trend - {unit_name}")
            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.text(0.5, 0.5, f"Error creating chart: {error_msg}", 
                   ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Cost Trend - {unit_name}", fontsize=14, fontweight='bold')
            return fig


def _create_static_single_unit_timeseries(df: pd.DataFrame, unit_name: str, client_name: str, max_savings_month: str, max_savings_amount: float):
    """Create static matplotlib version of single unit timeseries"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create the lines
    months = range(len(df))
    ax.plot(months, df['Grid Cost (₹)'], label='Grid Cost', marker='o', linewidth=2.5, color='#E53935')
    ax.plot(months, df['Actual Cost (₹)'], label='Actual Cost', marker='s', linewidth=2.5, color='#43A047')
    
    # Add annotation for maximum savings month
    max_savings_idx = df[df['Month'] == max_savings_month].index[0]
    max_savings_pos = df.index.get_loc(max_savings_idx)
    max_grid_cost = df.loc[max_savings_idx, 'Grid Cost (₹)']
    max_actual_cost = df.loc[max_savings_idx, 'Actual Cost (₹)']
    
    # Arrow pointing to the biggest gap
    ax.annotate(
        f'Max Savings\n₹{max_savings_amount/1e5:.1f}L\n({max_savings_month})',
        xy=(max_savings_pos, max_actual_cost),
        xytext=(max_savings_pos, max_grid_cost + max_grid_cost * 0.1),
        arrowprops=dict(arrowstyle='->', color='green', lw=2),
        fontsize=10,
        ha='center',
        bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7)
    )
    
    # Formatting
    ax.set_xlabel('Month', fontsize=12, fontweight='bold')
    ax.set_ylabel('Cost (₹ in Lakhs)', fontsize=12, fontweight='bold')
    title = f"Cost Trend Over Time - {unit_name}"
    if client_name:
        title += f"\n{client_name}"
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xticks(months)
    ax.set_xticklabels(df['Month'], rotation=45, ha='right')
    ax.yaxis.set_major_formatter(FuncFormatter(format_rupees_lakhs))
    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    logging.info(f"Successfully created static single unit timeseries for {unit_name}")
    return fig


def _create_interactive_single_unit_timeseries(df: pd.DataFrame, unit_name: str, client_name: str, max_savings_month: str, max_savings_amount: float):
    """Create interactive Plotly version of single unit timeseries"""
    
    fig = go.Figure()
    
    # Helper function to format currency
    def format_currency(value):
        if value >= 1e5:
            return f"₹{value / 1e5:.1f}L"
        else:
            return f"₹{value:,.0f}"
    
    # Grid Cost line
    fig.add_trace(go.Scatter(
        x=df['Month'],
        y=df['Grid Cost (₹)'],
        mode='lines+markers',
        name='Grid Cost',
        line=dict(color='#E53935', width=3),
        marker=dict(size=8, symbol='circle'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>⚡ Grid Cost:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val)] for val in df['Grid Cost (₹)']]
    ))
    
    # Actual Cost line
    fig.add_trace(go.Scatter(
        x=df['Month'],
        y=df['Actual Cost (₹)'],
        mode='lines+markers',
        name='Actual Cost',
        line=dict(color='#43A047', width=3),
        marker=dict(size=8, symbol='square'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>🏦 Actual Cost:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<b>💸 Savings:</b> %{customdata[1]}<br>' +
                     '<b>📊 Savings %:</b> %{customdata[2]}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val), 
                    format_currency(df.iloc[i]['Savings (₹)']),
                    f"{df.iloc[i]['Savings (%)']:.1f}%"] 
                   for i, val in enumerate(df['Actual Cost (₹)'])]
    ))
    
    # Add annotation for maximum savings month
    max_savings_row = df[df['Month'] == max_savings_month].iloc[0]
    fig.add_annotation(
        x=max_savings_month,
        y=max_savings_row['Grid Cost (₹)'] * 1.1,
        text=f"🎯 Max Savings<br>₹{max_savings_amount/1e5:.1f}L<br>({max_savings_row['Savings (%)']:.1f}%)",
        showarrow=True,
        arrowhead=2,
        arrowsize=1,
        arrowwidth=2,
        arrowcolor='green',
        font=dict(size=12, color='green'),
        bgcolor='rgba(144, 238, 144, 0.8)',
        bordercolor='green',
        borderwidth=2
    )
    
    # Update layout
    title = f"Cost Trend Over Time - {unit_name}"
    if client_name:
        title += f"<br>{client_name}"
    
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Month",
            tickangle=45
        ),
        yaxis=dict(
            title="Cost (₹ in Lakhs)",
            tickformat=',.0f'
        ),
        height=600,
        showlegend=True,
        hovermode='x unified'
    )
    
    logging.info(f"Successfully created interactive single unit timeseries for {unit_name}")
    return fig


def plot_single_unit_savings_trend(df: pd.DataFrame, unit_name: str, client_name: str = None, use_interactive: bool = False):
    """
    Create a savings trend line chart for a single unit with performance color coding.
    
    Args:
        df (pd.DataFrame): Single unit monthly cost data
        unit_name (str): Name of the unit
        client_name (str): Client name for title
        use_interactive (bool): Whether to use interactive Plotly chart
    
    Returns:
        matplotlib.figure.Figure or plotly.graph_objects.Figure: The chart figure
    """
    try:
        logging.info(f"Creating single unit savings trend for {unit_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for single unit savings trend - {unit_name}")
            if use_interactive:
                fig = go.Figure()
                fig.add_annotation(
                    text=f"No savings data available for {unit_name}",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5, xanchor='center', yanchor='middle',
                    font=dict(size=20, color="red")
                )
                fig.update_layout(title=f"Savings Trend - {unit_name}")
                return fig
            else:
                fig, ax = plt.subplots(figsize=(12, 6))
                ax.text(0.5, 0.5, f"No savings data available for {unit_name}", 
                       ha='center', va='center', fontsize=14, color='red')
                ax.set_title(f"Savings Trend - {unit_name}", fontsize=14, fontweight='bold')
                return fig
        
        # Sort by month
        df = df.sort_values('Month')
        
        # Validate required columns exist
        required_cols = ['Month', 'Savings (%)']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Check if savings percentage column has any valid data
        if df['Savings (%)'].isna().all() or df['Savings (%)'].empty:
            raise ValueError("No valid savings percentage data available")
        
        # Find min and max savings
        min_savings_idx = df['Savings (%)'].idxmin()
        max_savings_idx = df['Savings (%)'].idxmax()
        min_savings_month = df.loc[min_savings_idx, 'Month']
        max_savings_month = df.loc[max_savings_idx, 'Month']
        min_savings_pct = df.loc[min_savings_idx, 'Savings (%)']
        max_savings_pct = df.loc[max_savings_idx, 'Savings (%)']
        
        if use_interactive:
            return _create_interactive_single_unit_savings_trend(df, unit_name, client_name, min_savings_month, max_savings_month, min_savings_pct, max_savings_pct)
        else:
            return _create_static_single_unit_savings_trend(df, unit_name, client_name, min_savings_month, max_savings_month, min_savings_pct, max_savings_pct)
            
    except ZeroDivisionError as e:
        logging.error(f"ZeroDivisionError in single unit savings trend for {unit_name}: {str(e)}")
        error_msg = "Division by zero error - check data integrity"
        if use_interactive:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating chart: {error_msg}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=16, color="red")
            )
            fig.update_layout(title=f"Savings Trend - {unit_name}")
            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.text(0.5, 0.5, f"Error creating chart: {error_msg}", 
                   ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Savings Trend - {unit_name}", fontsize=14, fontweight='bold')
            return fig
    except Exception as e:
        error_msg = str(e) if str(e) else f"Unknown error occurred - Exception type: {type(e).__name__}"
        logging.error(f"Error creating single unit savings trend for {unit_name}: {error_msg}")
        logging.error(f"Exception details - Type: {type(e)}, Args: {e.args}")
        if use_interactive:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating chart: {error_msg}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=16, color="red")
            )
            fig.update_layout(title=f"Savings Trend - {unit_name}")
            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.text(0.5, 0.5, f"Error creating chart: {error_msg}", 
                   ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Savings Trend - {unit_name}", fontsize=14, fontweight='bold')
            return fig


def _create_static_single_unit_savings_trend(df: pd.DataFrame, unit_name: str, client_name: str, min_savings_month: str, max_savings_month: str, min_savings_pct: float, max_savings_pct: float):
    """Create static matplotlib version of single unit savings trend"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create color mapping for performance buckets
    def get_performance_color(savings_pct):
        if savings_pct < 10:
            return '#E53935'  # Red
        elif savings_pct < 25:
            return '#FF9800'  # Orange
        elif savings_pct < 50:
            return '#FFC107'  # Yellow
        else:
            return '#4CAF50'  # Green
    
    # Create the line
    months = range(len(df))
    colors = [get_performance_color(pct) for pct in df['Savings (%)']]
    
    # Main line
    ax.plot(months, df['Savings (%)'], linewidth=2.5, color='#1976D2', alpha=0.7)
    
    # Colored markers for performance buckets
    for i, (month, savings_pct) in enumerate(zip(months, df['Savings (%)'])):
        ax.scatter(month, savings_pct, color=colors[i], s=100, zorder=5)
    
    # Add annotations for min and max
    min_savings_pos = df[df['Month'] == min_savings_month].index[0]
    min_pos = df.index.get_loc(min_savings_pos)
    max_savings_pos = df[df['Month'] == max_savings_month].index[0]
    max_pos = df.index.get_loc(max_savings_pos)
    
    # Min savings annotation
    ax.annotate(
        f'Min Savings\n{min_savings_pct:.1f}%\n({min_savings_month})',
        xy=(min_pos, min_savings_pct),
        xytext=(min_pos, min_savings_pct - 5),
        arrowprops=dict(arrowstyle='->', color='red', lw=2),
        fontsize=9,
        ha='center',
        bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.7)
    )
    
    # Max savings annotation
    ax.annotate(
        f'Max Savings\n{max_savings_pct:.1f}%\n({max_savings_month})',
        xy=(max_pos, max_savings_pct),
        xytext=(max_pos, max_savings_pct + 5),
        arrowprops=dict(arrowstyle='->', color='green', lw=2),
        fontsize=9,
        ha='center',
        bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7)
    )
    
    # Add performance legend
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#E53935', markersize=8, label='< 10% (Poor)'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#FF9800', markersize=8, label='10-25% (Fair)'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#FFC107', markersize=8, label='25-50% (Good)'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#4CAF50', markersize=8, label='> 50% (Excellent)')
    ]
    ax.legend(handles=legend_elements, loc='upper right', title='Performance Buckets')
    
    # Formatting
    ax.set_xlabel('Month', fontsize=12, fontweight='bold')
    ax.set_ylabel('Savings (%)', fontsize=12, fontweight='bold')
    title = f"Savings Trend - {unit_name}"
    if client_name:
        title += f"\n{client_name}"
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xticks(months)
    ax.set_xticklabels(df['Month'], rotation=45, ha='right')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    logging.info(f"Successfully created static single unit savings trend for {unit_name}")
    return fig


def _create_interactive_single_unit_savings_trend(df: pd.DataFrame, unit_name: str, client_name: str, min_savings_month: str, max_savings_month: str, min_savings_pct: float, max_savings_pct: float):
    """Create interactive Plotly version of single unit savings trend"""
    
    fig = go.Figure()
    
    # Create color mapping for performance buckets
    def get_performance_color(savings_pct):
        if savings_pct < 10:
            return '#E53935'  # Red
        elif savings_pct < 25:
            return '#FF9800'  # Orange
        elif savings_pct < 50:
            return '#FFC107'  # Yellow
        else:
            return '#4CAF50'  # Green
    
    def get_performance_label(savings_pct):
        if savings_pct < 10:
            return 'Poor (< 10%)'
        elif savings_pct < 25:
            return 'Fair (10-25%)'
        elif savings_pct < 50:
            return 'Good (25-50%)'
        else:
            return 'Excellent (> 50%)'
    
    # Helper function to format currency
    def format_currency(value):
        if value >= 1e5:
            return f"₹{value / 1e5:.1f}L"
        else:
            return f"₹{value:,.0f}"
    
    colors = [get_performance_color(pct) for pct in df['Savings (%)']]
    
    # Main line
    fig.add_trace(go.Scatter(
        x=df['Month'],
        y=df['Savings (%)'],
        mode='lines+markers',
        name='Savings Trend',
        line=dict(color='#1976D2', width=3),
        marker=dict(
            size=12,
            color=colors,
            line=dict(width=2, color='white')
        ),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>📊 Savings:</b> %{y:.1f}%<br>' +
                     '<b>💰 Savings Amount:</b> %{customdata[0]}<br>' +
                     '<b>🎯 Performance:</b> %{customdata[1]}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(df.iloc[i]['Savings (₹)']), 
                    get_performance_label(pct)] 
                   for i, pct in enumerate(df['Savings (%)'])]
    ))
    
    # Add annotations for min and max
    fig.add_annotation(
        x=min_savings_month,
        y=min_savings_pct - 2,
        text=f"📉 Min Savings<br>{min_savings_pct:.1f}%",
        showarrow=True,
        arrowhead=2,
        arrowsize=1,
        arrowwidth=2,
        arrowcolor='red',
        font=dict(size=11, color='red'),
        bgcolor='rgba(255, 182, 193, 0.8)',
        bordercolor='red',
        borderwidth=2
    )
    
    fig.add_annotation(
        x=max_savings_month,
        y=max_savings_pct + 2,
        text=f"📈 Max Savings<br>{max_savings_pct:.1f}%",
        showarrow=True,
        arrowhead=2,
        arrowsize=1,
        arrowwidth=2,
        arrowcolor='green',
        font=dict(size=11, color='green'),
        bgcolor='rgba(144, 238, 144, 0.8)',
        bordercolor='green',
        borderwidth=2
    )
    
    # Update layout
    title = f"Savings Trend - {unit_name}"
    if client_name:
        title += f"<br>{client_name}"
    
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Month",
            tickangle=45
        ),
        yaxis=dict(
            title="Savings (%)",
            tickformat='.1f'
        ),
        height=600,
        showlegend=False,
        annotations=[
            dict(
                text="Performance Buckets:<br>"
                     "🔴 Poor (< 10%) | 🟠 Fair (10-25%)<br>"
                     "🟡 Good (25-50%) | 🟢 Excellent (> 50%)",
                xref="paper", yref="paper",
                x=0.02, y=0.98,
                xanchor='left', yanchor='top',
                font=dict(size=10),
                bgcolor='rgba(255,255,255,0.8)',
                bordercolor='gray',
                borderwidth=1
            )
        ]
    )
    
    logging.info(f"Successfully created interactive single unit savings trend for {unit_name}")
    return fig
