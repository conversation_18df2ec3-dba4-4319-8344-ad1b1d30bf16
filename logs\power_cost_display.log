2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:02:40,390 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:02:41,577 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:02:41,632 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:02:41,632 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:02:41,649 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:02:41,649 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:02:41,657 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:02:41,682 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:02:41,682 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 12:02:53,766 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:02:53,766 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:02:53,770 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:02:53,770 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:02:53,772 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:02:55,010 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:02:55,011 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:02:55,011 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:02:55,014 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:02:55,017 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:02:55,018 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:02:55,042 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:02:55,042 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 12:28:18,135 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:28:18,137 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:28:18,139 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:28:18,139 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:28:18,139 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:28:19,861 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:28:19,863 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 12:28:30,116 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:28:30,117 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:28:30,118 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:28:30,120 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:28:30,120 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:28:31,300 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:28:31,301 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:28:31,301 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:28:31,303 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:28:31,304 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:28:31,307 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:28:31,325 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:28:31,328 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 14:54:08,823 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 14:54:08,823 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 14:54:08,824 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 14:54:08,826 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 14:54:08,827 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 14:54:10,313 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 14:54:10,313 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 14:54:10,313 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 14:54:10,326 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 14:54:10,329 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 14:54:10,329 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 14:54:10,363 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 14:54:10,367 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 14:54:19,249 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 14:54:19,249 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 14:54:19,249 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 14:54:19,265 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 14:54:19,265 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 14:54:19,265 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 14:54:19,300 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 14:54:19,300 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:48:22,654 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:48:22,677 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:48:22,677 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:51:00,806 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:51:00,808 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:51:00,810 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:51:00,811 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:51:00,813 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:51:01,971 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:51:01,971 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:51:01,971 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:51:01,974 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:51:01,975 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:51:01,976 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:51:02,008 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:51:02,011 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:51:18,234 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:51:18,236 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:51:18,236 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:51:18,239 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:51:18,240 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:51:19,140 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:51:19,140 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:51:19,140 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:51:19,152 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:51:19,152 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:51:19,156 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:51:19,172 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:51:19,176 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:53:10,143 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:53:10,143 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:53:10,143 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:53:10,158 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:53:10,159 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:53:10,162 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:53:10,180 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:53:10,184 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:03:12,887 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:03:12,903 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:03:12,903 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:03:12,905 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:03:12,906 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:03:12,908 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:03:12,925 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:03:12,928 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:04:54,290 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:04:54,291 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:04:54,292 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:04:54,293 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:04:54,294 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:04:55,288 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:04:55,288 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:04:55,288 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:04:55,291 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:04:55,292 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:04:55,296 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:04:55,316 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:04:55,320 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:05:25,196 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:05:25,197 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:05:25,199 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:05:25,200 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:05:25,200 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:05:26,271 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:05:26,271 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:05:26,271 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:05:26,274 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:05:26,275 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:05:26,276 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:05:26,299 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:05:26,302 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:05:51,133 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:05:51,134 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:05:51,134 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:05:51,135 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:05:51,135 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:05:52,170 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:05:52,170 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:05:52,171 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:05:52,174 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:05:52,175 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:05:52,178 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:05:52,201 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:05:52,204 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:07:20,565 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:07:20,565 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:07:20,565 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:07:20,569 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:07:20,571 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:07:20,573 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:07:20,596 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:07:20,601 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:07:51,675 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:07:52,608 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:07:52,609 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:07:52,609 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:07:52,613 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:07:52,614 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:07:55,377 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:07:55,377 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:07:55,379 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:07:55,379 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:07:55,379 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:07:56,414 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:07:56,414 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:07:56,414 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:07:56,418 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:07:56,419 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:07:56,421 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:07:56,439 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:07:56,443 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-09 12:04:42,315 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 12:04:42,379 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 12:04:42,387 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 12:04:42,387 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 12:04:42,387 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 12:04:45,245 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 12:04:45,245 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 12:04:45,246 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 12:04:45,249 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 12:04:45,249 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 12:04:45,261 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 12:04:45,373 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-09 12:04:45,381 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-09 12:06:50,727 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 12:06:50,727 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 12:06:50,732 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 12:06:50,733 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 12:06:50,733 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 12:06:51,969 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 12:06:51,996 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-09 12:06:52,000 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 12:07:27,718 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 12:07:27,718 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 12:07:27,733 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 12:07:27,733 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 12:07:27,733 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 12:07:27,751 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 14:03:02,433 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:03:02,434 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:03:02,437 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:03:02,439 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 14:03:02,439 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:03:03,983 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 14:03:03,985 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 14:04:25,184 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:04:25,184 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:04:25,194 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:04:25,197 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:04:25,202 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:04:25,202 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:04:42,667 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:04:42,667 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:04:42,669 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:04:42,671 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:04:42,672 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:04:42,672 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:04:43,581 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:04:43,582 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:04:43,582 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:04:43,585 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:04:43,585 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:04:43,588 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:05:01,942 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:05:01,942 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 6.0
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:05:03,118 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:05:03,118 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:05:03,118 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:05:03,129 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:05:03,131 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:05:03,135 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:05:15,850 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:05:15,851 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:05:15,853 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:05:15,854 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:05:15,855 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 1.0
2025-07-09 14:05:15,855 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:05:16,788 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:05:16,788 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:05:16,788 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:05:16,801 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:05:16,802 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:05:16,805 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:27:36,145 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:27:36,145 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:27:36,147 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:27:36,151 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:27:36,152 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 1.0
2025-07-09 14:27:36,153 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:27:37,693 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:27:37,695 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:27:37,695 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:27:37,701 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:27:37,701 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:27:37,707 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:28:37,272 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:28:37,273 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:28:37,273 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:28:37,275 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:28:37,275 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:28:45,007 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:28:45,007 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:28:45,013 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: With Banking
2025-07-09 14:28:45,013 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:28:45,017 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:28:45,019 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:28:46,799 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:28:46,799 - INFO - power_cost_display - power_cost_display.py:143 - Processing 'With Banking' analysis
2025-07-09 14:28:46,799 - INFO - power_cost_display - power_cost_display.py:146 - Calculating monthly power costs with banking
2025-07-09 14:28:46,806 - INFO - power_cost_display - power_cost_display.py:154 - Successfully calculated power costs with banking, 6 records
2025-07-09 14:28:46,806 - INFO - power_cost_display - power_cost_display.py:159 - Successfully generated summary table for banking analysis
2025-07-09 14:28:46,812 - INFO - power_cost_display - power_cost_display.py:200 - Successfully displayed banking analysis metrics
2025-07-09 14:49:46,441 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:49:46,442 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:49:46,443 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:49:46,447 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:49:46,448 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:49:46,452 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:49:48,140 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:49:48,142 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:49:48,142 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:49:48,146 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:49:48,148 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:49:48,152 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:58:21,655 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:58:21,655 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:58:21,667 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:58:21,669 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:58:21,672 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:58:21,673 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:58:23,338 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:58:23,338 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:58:23,339 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:58:23,343 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:58:23,345 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:58:23,349 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:02:17,644 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:02:17,644 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:02:17,644 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:02:17,650 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:02:17,651 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:02:17,651 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:02:18,670 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:02:18,671 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:02:18,671 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:02:18,674 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:02:18,675 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:02:18,678 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:02:40,290 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:02:40,298 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:02:40,299 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:02:40,302 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:02:40,302 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:02:40,303 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:02:41,339 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:02:41,339 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:02:41,339 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:02:41,357 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:02:41,358 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:02:41,362 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:03:24,821 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:03:24,822 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:03:24,823 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:03:24,826 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:03:24,827 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:03:24,827 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:03:25,774 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:03:25,774 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:03:25,774 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:03:25,778 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:03:25,780 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:03:25,783 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:03:45,896 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:03:45,897 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:03:45,898 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:03:45,899 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:03:45,901 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:03:45,901 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:03:46,813 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:03:46,813 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:03:46,813 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:03:46,819 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:03:46,820 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:03:46,821 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:07:53,032 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:07:53,033 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:07:53,035 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:07:53,035 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:07:53,035 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:07:53,035 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:07:54,836 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:07:54,837 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:07:54,837 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:07:54,845 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:07:54,846 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:07:54,846 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:11:22,183 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:11:22,183 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:11:22,183 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:11:22,188 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:11:22,188 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:11:22,194 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:11:23,883 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:11:23,883 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:11:23,884 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:11:23,888 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:11:23,889 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:11:23,894 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:13:43,239 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:13:43,239 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:13:43,239 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:13:43,249 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:13:43,250 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:13:43,250 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:13:44,169 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:13:44,169 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:13:44,169 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:13:44,172 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:13:44,172 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:13:44,172 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:14:36,849 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:14:36,850 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:14:36,853 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:14:36,854 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:14:36,855 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:14:36,855 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:14:37,887 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:14:37,889 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:14:37,889 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:14:37,893 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:14:37,894 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:14:37,895 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:15:03,254 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:15:03,254 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:15:03,255 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:15:03,257 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:15:03,257 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:15:03,259 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:15:04,241 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:15:04,241 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:15:04,241 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:15:04,246 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:15:04,247 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:15:04,249 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:15:26,683 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:15:26,684 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:15:26,685 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:15:26,687 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:15:26,688 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:15:26,689 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:15:28,311 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:15:28,311 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:15:28,326 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:15:28,326 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:15:28,326 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:15:28,326 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:16:14,603 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:16:14,604 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:16:14,605 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:16:14,607 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:16:14,608 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:16:14,609 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:16:15,621 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:16:15,621 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:16:15,622 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:16:15,626 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:16:15,628 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:16:15,629 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:16:29,038 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:16:29,038 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:16:29,038 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:16:29,038 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:16:29,038 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:16:29,038 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:16:30,706 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:16:30,706 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:16:30,706 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:16:30,710 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:16:30,711 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:16:30,715 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:18:36,093 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:18:36,102 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:18:36,105 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:18:36,107 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:18:36,108 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:18:36,108 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:18:37,384 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:18:37,384 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:18:37,384 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:18:37,384 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:18:37,384 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:18:37,402 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 15:20:57,936 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 15:20:57,936 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 15:20:57,946 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 15:20:57,949 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 15:20:57,950 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 15:20:57,951 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 15:20:59,805 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 15:20:59,805 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 15:20:59,806 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 15:20:59,812 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 15:20:59,812 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 15:20:59,814 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:22:20,801 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:22:20,801 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:22:20,818 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:22:20,820 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:22:20,822 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:22:20,823 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:22:22,652 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:22:22,652 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:22:22,654 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:22:22,655 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:22:22,655 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:22:22,670 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:22:44,755 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:22:44,755 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:22:44,755 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:22:44,755 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:22:44,755 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:22:44,755 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:22:46,556 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:22:46,558 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:22:46,558 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:22:46,562 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:22:46,564 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:22:46,570 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:23:40,479 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:23:40,479 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:23:40,479 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:23:40,492 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:23:40,494 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:23:40,494 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:23:42,292 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:23:42,292 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:23:42,292 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:23:42,292 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:23:42,292 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:23:42,309 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:23:52,430 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:23:52,430 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:23:52,432 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:23:52,434 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:23:52,436 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:23:52,436 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:23:54,076 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:23:54,076 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:23:54,076 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:23:54,076 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:23:54,076 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:23:54,098 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:24:11,050 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:24:11,051 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:24:11,054 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:24:11,059 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:24:11,063 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:24:11,064 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:24:12,879 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:24:12,879 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:24:12,882 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:24:12,884 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:24:12,884 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:24:12,889 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:26:37,321 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:26:37,322 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:26:37,322 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:26:37,326 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:26:37,327 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:26:37,327 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:26:38,642 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:26:38,644 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:26:38,644 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:26:38,646 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:26:38,648 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:26:38,650 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:28:46,166 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:28:46,166 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:28:46,166 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:28:46,179 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:28:46,181 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:28:46,182 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:28:47,399 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:28:47,400 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:28:47,400 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:28:47,404 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:28:47,405 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:28:47,408 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:29:48,018 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:29:48,019 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:29:48,020 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:29:48,022 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:29:48,023 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:29:48,023 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:29:48,952 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:29:48,952 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:29:48,952 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:29:48,956 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:29:48,956 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:29:48,959 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:30:58,959 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:30:58,959 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:30:58,974 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:30:58,976 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:30:58,977 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:30:58,978 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:30:59,875 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:30:59,875 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:30:59,875 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:30:59,885 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:30:59,886 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:30:59,889 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:31:15,071 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:31:15,073 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:31:15,074 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:31:15,075 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:31:15,076 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:31:15,077 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:31:16,025 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:31:16,025 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:31:16,025 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:31:16,037 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:31:16,038 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:31:16,041 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:31:48,678 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:31:48,679 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:31:48,680 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:31:48,683 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:31:48,685 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:31:48,686 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:31:50,304 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:31:50,306 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:31:50,306 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:31:50,311 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:31:50,311 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:31:50,311 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:32:05,407 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:32:05,407 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:32:05,409 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:32:05,409 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:32:05,411 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:32:05,412 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:32:06,879 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:32:06,879 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:32:06,879 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:32:06,883 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:32:06,883 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:32:06,883 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:53:04,186 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:53:04,187 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:53:04,188 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:53:04,190 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:53:04,190 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:53:04,191 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:53:05,054 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:53:05,054 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:53:05,054 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:53:05,069 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:53:05,070 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:53:05,072 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 16:54:36,481 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 16:54:36,481 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 16:54:36,481 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 16:54:36,490 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 16:54:36,491 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 16:54:36,491 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 16:54:37,721 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 16:54:37,722 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 16:54:37,722 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 16:54:37,726 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 16:54:37,728 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 16:54:37,729 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:08:39,613 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:08:39,613 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:08:39,613 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:08:39,625 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:08:39,627 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:08:39,627 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:08:41,244 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:08:41,244 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:08:41,246 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:08:41,250 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:08:41,253 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:08:41,260 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:08:54,114 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:08:54,115 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:08:54,116 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:08:54,117 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:08:54,118 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:08:54,119 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:08:55,121 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:08:55,122 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:08:55,122 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:08:55,124 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:08:55,125 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:08:55,130 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:11:34,526 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:11:34,526 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:11:34,528 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:11:34,530 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:11:34,532 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:11:34,534 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:11:36,140 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:11:36,140 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:11:36,140 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:11:36,156 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:11:36,156 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:11:36,156 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:13:49,581 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:13:49,581 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:13:49,583 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:13:49,585 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:13:49,587 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:13:49,588 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:13:51,003 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:13:51,003 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:13:51,003 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:13:51,016 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:13:51,018 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:13:51,019 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:15:16,510 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:15:16,510 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:15:16,510 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:15:16,515 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:15:16,515 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:15:16,515 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:15:18,177 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:15:18,177 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:15:18,177 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:15:18,197 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:15:18,201 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:15:18,213 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:15:48,431 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:15:48,432 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:15:48,433 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:15:48,435 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:15:48,436 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:15:48,437 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:15:49,399 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:15:49,399 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:15:49,399 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:15:49,402 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:15:49,403 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:15:49,406 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:16:07,530 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:16:07,532 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:16:07,533 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:16:07,534 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:16:07,534 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:16:07,535 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:16:08,441 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:16:08,441 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:16:08,441 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:16:08,446 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:16:08,447 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:16:08,449 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:16:14,186 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:16:14,186 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:16:14,187 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:16:14,189 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:16:14,190 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:16:14,190 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:16:15,121 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:16:15,121 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:16:15,121 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:16:15,126 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:16:15,128 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:16:15,130 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:16:31,378 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:16:31,378 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:16:31,378 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:16:31,378 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:16:31,378 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:16:31,387 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:16:32,299 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:16:32,299 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:16:32,299 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:16:32,308 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:16:32,309 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:16:32,311 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:20:01,661 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:20:01,661 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:20:01,676 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:20:01,678 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:20:01,679 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:20:01,680 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:20:02,658 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:20:02,659 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:20:02,659 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:20:02,662 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:20:02,663 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:20:02,666 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:24:51,615 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:24:51,615 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:24:51,616 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:24:51,619 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:24:51,619 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:24:51,619 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:24:53,059 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:24:53,059 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:24:53,059 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:24:53,081 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:24:53,081 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:24:53,081 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:26:12,404 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:26:12,405 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:26:12,406 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:26:12,408 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:26:12,409 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:26:12,409 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:26:14,238 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:26:14,238 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:26:14,239 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:26:14,242 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:26:14,242 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:26:14,255 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:27:44,961 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:27:44,961 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:27:44,965 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:27:44,969 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:27:44,972 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:27:44,972 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:27:46,619 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:27:46,620 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:27:46,620 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:27:46,626 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:27:46,626 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:27:46,626 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:29:46,870 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:29:46,870 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:29:46,870 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:29:46,870 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:29:46,870 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:29:46,870 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:29:48,387 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:29:48,387 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:29:48,387 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:29:48,402 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:29:48,404 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:29:48,408 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:34:33,925 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:34:33,940 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:34:33,942 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:34:33,943 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:34:33,945 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:34:33,946 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:34:34,857 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:34:34,857 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:34:34,857 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:34:34,871 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:34:34,872 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:34:34,876 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:35:02,098 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:35:02,109 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:35:02,111 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:35:02,113 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:35:02,114 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:35:02,114 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:35:03,215 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:35:03,215 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:35:03,215 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:35:03,224 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:35:03,225 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:35:03,230 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:35:17,278 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:35:17,278 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:35:17,278 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:35:17,278 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:35:17,278 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:35:17,278 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:35:18,894 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:35:18,894 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:35:18,894 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:35:18,911 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:35:18,911 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:35:18,911 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:35:36,497 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:35:36,497 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:35:36,513 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:35:36,515 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:35:36,516 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:35:36,517 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:35:38,029 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:35:38,029 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:35:38,029 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:35:38,047 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:35:38,047 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:35:38,047 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:35:47,012 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:35:47,012 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:35:47,012 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:35:47,029 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:35:47,030 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:35:47,031 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:35:48,573 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:35:48,575 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:35:48,575 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:35:48,581 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:35:48,582 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:35:48,589 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:36:06,965 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:36:06,965 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:36:06,965 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:36:06,965 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:36:06,965 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:36:06,980 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:36:08,481 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:36:08,481 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:36:08,481 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:36:08,481 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:36:08,497 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:36:08,501 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:41:10,364 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:41:10,364 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:41:10,365 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:41:10,367 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:41:10,368 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:41:10,368 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:41:11,366 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:41:11,366 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:41:11,366 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:41:11,371 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:41:11,371 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:41:11,375 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:41:34,416 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:41:34,416 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:41:34,416 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:41:34,424 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:41:34,425 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:41:34,426 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:41:36,337 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:41:36,337 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:41:36,337 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:41:36,355 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:41:36,355 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:41:36,355 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:42:39,100 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:42:39,100 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:42:39,100 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:42:39,106 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:42:39,106 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:42:39,107 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:42:40,116 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:42:40,116 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:42:40,116 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:42:40,121 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:42:40,121 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:42:40,124 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:44:52,105 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:44:52,105 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:44:52,107 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:44:52,109 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:44:52,110 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:44:52,111 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:44:53,553 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:44:53,553 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:44:53,555 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:44:53,558 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:44:53,558 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:44:53,558 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:45:07,183 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:45:07,183 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:45:07,183 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:45:07,199 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:45:07,199 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:45:07,199 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:45:08,724 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:45:08,725 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:45:08,726 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:45:08,727 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:45:08,727 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:45:08,739 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:46:33,195 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:46:33,200 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:46:33,200 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:46:33,203 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:46:33,203 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:46:33,204 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:46:34,176 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:46:34,176 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:46:34,176 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:46:34,179 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:46:34,180 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:46:34,183 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:48:53,070 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:48:53,070 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:48:53,071 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:48:53,073 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:48:53,074 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:48:53,075 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:48:54,475 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:48:54,475 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:48:54,475 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:48:54,490 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:48:54,490 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:48:54,495 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:49:03,756 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:49:03,756 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:49:03,756 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:49:03,756 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:49:03,756 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:49:03,767 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:49:05,047 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:49:05,047 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:49:05,047 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:49:05,053 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:49:05,054 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:49:10,743 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:49:10,743 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:49:10,745 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:49:10,746 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:49:10,748 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:49:10,748 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:49:11,691 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:49:11,691 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:49:11,693 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:49:11,697 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:49:11,698 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:49:11,701 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:49:24,242 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:49:24,242 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:49:24,242 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:49:24,248 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:49:24,249 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:49:24,249 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:49:25,184 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:49:25,184 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:49:25,184 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:49:25,197 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:49:25,197 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:49:25,199 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 17:52:00,452 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 17:52:00,452 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 17:52:00,455 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 17:52:00,458 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 17:52:00,458 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 17:52:00,459 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 17:52:02,317 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 17:52:02,317 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 17:52:02,321 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 17:52:02,321 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 17:52:02,321 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 17:52:02,332 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 10:55:33,514 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 10:55:33,514 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 10:55:33,514 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 10:55:33,514 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 10:55:33,514 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 10:55:33,514 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 10:55:35,131 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 10:55:35,131 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 10:55:35,131 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 10:55:35,131 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 10:55:35,131 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 10:55:35,148 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:06:42,080 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:06:42,080 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:06:42,081 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:06:42,083 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:06:42,085 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:06:42,086 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:06:43,535 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:06:43,536 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:06:43,536 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:06:43,536 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:06:43,536 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:06:43,547 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:08:09,607 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:08:09,607 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:08:09,611 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:08:09,611 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:08:09,611 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:08:09,611 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:08:11,425 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:08:11,425 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:08:11,425 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:08:11,429 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:08:11,429 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:08:20,920 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:08:20,923 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:08:20,925 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:08:20,927 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:08:20,929 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:08:20,929 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:08:22,215 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:08:22,215 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:08:22,215 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:08:22,215 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:08:22,215 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:08:22,231 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:10:06,791 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:10:06,793 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:10:06,795 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:10:06,797 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:10:06,798 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:10:06,799 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:10:08,329 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:10:08,329 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:10:08,329 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:10:08,329 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:10:08,345 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:10:08,348 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:14:38,598 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:14:38,598 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:14:38,598 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:14:38,615 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:14:38,617 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:14:38,618 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:14:40,201 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:14:40,201 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:14:40,201 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:14:40,201 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:14:40,201 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:14:40,214 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:20:05,213 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:20:05,213 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:20:05,217 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:20:05,219 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:20:05,221 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:20:05,222 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:20:06,700 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:20:06,700 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:20:06,701 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:20:06,705 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:20:06,705 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:20:06,705 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:24:14,223 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:24:14,229 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:24:14,229 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:24:14,233 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:24:14,238 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:24:14,239 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:24:15,757 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:24:15,758 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:24:15,758 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:24:15,763 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:24:15,766 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:24:15,771 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:27:49,290 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:27:49,290 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:27:49,291 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:27:49,293 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:27:49,294 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:27:49,294 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:27:50,447 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:27:50,449 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:27:50,449 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:27:50,454 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:27:50,455 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:27:50,455 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:29:38,338 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:29:38,339 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:29:38,340 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:29:38,342 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:29:38,343 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:29:38,344 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:29:39,327 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:29:39,327 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:29:39,327 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:29:39,332 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:29:39,333 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:29:39,335 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:36:20,368 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:36:20,374 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:36:20,374 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:36:20,374 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:36:20,374 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:36:20,374 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:36:22,037 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:36:22,037 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:36:22,037 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:36:22,052 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:36:22,054 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:36:22,054 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:37:33,507 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:37:33,507 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:37:33,508 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:37:33,509 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:37:33,511 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:37:33,511 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:37:35,065 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:37:35,065 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:37:35,065 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:37:35,082 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:37:35,082 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:37:35,082 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:38:27,811 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:38:27,811 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:38:27,814 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:38:27,816 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:38:27,819 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:38:27,821 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:38:28,960 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:38:28,960 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:38:28,960 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:38:28,969 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:38:28,969 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:38:28,974 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:38:42,238 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:38:42,238 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:38:42,238 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:38:42,238 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:38:42,238 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:38:42,238 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:38:43,871 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:38:43,871 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:38:43,871 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:38:43,887 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:38:43,887 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:38:43,887 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:39:33,024 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:39:33,025 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:39:33,026 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:39:33,028 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:39:33,029 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:39:33,029 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:39:33,907 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:39:33,907 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:39:33,907 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:39:33,916 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:39:33,916 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:39:33,919 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:54:23,805 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:54:23,805 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:54:23,807 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:54:23,810 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:54:23,811 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:54:23,812 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:54:25,358 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:54:25,360 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:54:25,360 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:54:25,361 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:54:25,361 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:54:25,374 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:54:38,640 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:54:38,640 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:54:38,641 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:54:38,643 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:54:38,645 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:54:38,646 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:54:39,590 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:54:39,590 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:54:39,590 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:54:39,606 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:54:39,606 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:54:39,610 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 11:57:39,658 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 11:57:39,658 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 11:57:39,668 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 11:57:39,670 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 11:57:39,671 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 11:57:39,671 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 11:57:40,619 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 11:57:40,619 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 11:57:40,619 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 11:57:40,632 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 11:57:40,633 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 11:57:40,636 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:08:49,429 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:08:49,429 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:08:49,432 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:08:49,433 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:08:49,434 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:08:49,434 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:08:50,387 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:08:50,388 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:08:50,388 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:08:50,392 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:08:50,394 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:08:50,396 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:11:43,964 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:11:43,964 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:11:43,981 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:11:43,984 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:11:43,985 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:11:43,986 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:11:45,364 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:11:45,364 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:11:45,364 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:11:45,380 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:11:45,380 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:11:45,380 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:12:52,329 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:12:52,330 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:12:52,330 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:12:52,332 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:12:52,332 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:12:52,332 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:12:53,271 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:12:53,271 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:12:53,271 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:12:53,276 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:12:53,277 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:12:53,280 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:14:01,197 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:14:01,199 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:14:01,199 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:14:01,200 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:14:01,202 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:14:01,202 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:14:02,144 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:14:02,144 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:14:02,144 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:14:02,151 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:14:02,152 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:14:02,154 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:15:12,314 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:15:12,314 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:15:12,315 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:15:12,317 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:15:12,320 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:15:12,320 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:15:13,270 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:15:13,272 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:15:13,272 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:15:13,275 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:15:13,277 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:15:13,286 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:17:35,364 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:17:35,366 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:17:35,367 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:17:35,369 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:17:35,373 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:17:35,373 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:17:37,062 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:17:37,062 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:17:37,062 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:17:37,062 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:17:37,062 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:17:37,075 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:19:11,525 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:19:11,525 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:19:11,526 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:19:11,528 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:19:11,529 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:19:11,529 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:19:12,449 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:19:12,450 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:19:12,450 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:19:12,452 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:19:12,452 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:19:12,457 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:21:22,537 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:21:22,537 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:21:22,539 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:21:22,541 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:21:22,542 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:21:22,542 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:21:23,594 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:21:23,594 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:21:23,595 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:21:23,597 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:21:23,597 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:21:23,600 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:22:24,390 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:22:24,391 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:22:24,392 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:22:24,392 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:22:24,395 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:22:24,395 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:22:25,594 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:22:25,594 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:22:25,594 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:22:25,608 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:22:25,611 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:22:25,611 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:24:12,363 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:24:12,364 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:24:12,365 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:24:12,366 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:24:12,368 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:24:12,368 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:24:13,368 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:24:13,368 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:24:13,368 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:24:13,374 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:24:13,375 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:24:13,377 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:24:40,460 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:24:40,460 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:24:40,461 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:24:40,461 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:24:40,461 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:24:40,465 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:24:41,452 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:24:41,452 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:24:41,452 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:24:41,472 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:24:41,473 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:24:41,476 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:25:29,245 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:25:29,247 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:25:29,247 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:25:29,251 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:25:29,251 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:25:29,251 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:25:30,924 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:25:30,924 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:25:30,925 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:25:30,928 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:25:30,929 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:25:30,933 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:27:12,471 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:27:12,472 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:27:12,473 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:27:12,475 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:27:12,478 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:27:12,478 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:27:13,571 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:27:13,571 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:27:13,571 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:27:13,585 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:27:13,586 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:27:13,588 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:30:04,519 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:30:04,519 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:30:04,519 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:30:04,524 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:30:04,524 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:30:04,524 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:30:06,244 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:30:06,244 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:30:06,244 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:30:06,244 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:30:06,244 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:30:06,244 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:30:49,691 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:30:49,691 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:30:49,692 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:30:49,694 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:30:49,695 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:30:49,695 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:30:50,847 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:30:50,847 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:30:50,847 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:30:50,847 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:30:50,847 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:30:50,864 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:34:04,103 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:34:04,103 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:34:04,103 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:34:04,113 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:34:04,116 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:34:04,117 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:34:05,545 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:34:05,545 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:34:05,545 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:34:05,562 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:34:05,562 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:34:05,562 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:34:49,676 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:34:49,676 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:34:49,677 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:34:49,679 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:34:49,680 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:34:49,680 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:34:50,599 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:34:50,599 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:34:50,604 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:34:50,607 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:34:50,607 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:34:50,610 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:36:25,825 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:36:25,825 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:36:25,840 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:36:25,844 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:36:25,844 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:36:25,846 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:36:27,257 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:36:27,257 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:36:27,257 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:36:27,257 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:36:27,257 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:36:27,274 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:37:21,157 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:37:21,157 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:37:21,160 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:37:21,162 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:37:21,163 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:37:21,164 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:37:22,109 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:37:22,109 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:37:22,109 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:37:22,117 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:37:22,118 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:37:22,120 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:39:43,894 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:39:43,895 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:39:43,896 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:39:43,897 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:39:43,898 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:39:43,898 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:39:45,048 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:39:45,048 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:39:45,049 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:39:45,053 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:39:45,054 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:39:45,056 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:42:12,663 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:42:12,664 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:42:12,667 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:42:12,669 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:42:12,670 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:42:12,670 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:42:14,419 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:42:14,419 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:42:14,419 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:42:14,430 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:42:14,433 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:42:14,436 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:44:10,565 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:44:10,566 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:44:10,568 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:44:10,570 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:44:10,571 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:44:10,573 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:44:12,355 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:44:12,370 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:44:12,370 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:44:12,376 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:44:12,376 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:44:12,376 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:45:40,800 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:45:40,801 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:45:40,802 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:45:40,803 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:45:40,803 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:45:40,803 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:45:41,819 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:45:41,819 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:45:41,819 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:45:41,832 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:45:41,834 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:45:41,835 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:45:51,261 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:45:51,261 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:45:51,265 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:45:51,267 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:45:51,268 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:45:51,270 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:45:52,246 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:45:52,246 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:45:52,246 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:45:52,252 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:45:52,253 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:45:52,255 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:47:51,919 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:47:51,919 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:47:51,921 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:47:51,922 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:47:51,923 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:47:51,923 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:47:53,655 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:47:53,655 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:47:53,655 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:47:53,655 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:47:53,672 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:47:53,678 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:49:20,196 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:49:20,197 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:49:20,197 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:49:20,200 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:49:20,201 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:49:20,201 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:49:22,118 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:49:22,118 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:49:22,118 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:49:22,122 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:49:22,124 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:49:22,127 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 12:52:32,219 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 12:52:32,219 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 12:52:32,220 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 12:52:32,222 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 12:52:32,223 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 12:52:32,224 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 12:52:33,318 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 12:52:33,324 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 12:52:33,324 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 12:52:33,325 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 12:52:33,325 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 12:52:33,325 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:24:56,062 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:24:56,078 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:24:56,080 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:24:56,083 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:24:56,084 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:24:56,085 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:24:57,580 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:24:57,580 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:24:57,580 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:24:57,596 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:24:57,596 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:24:57,596 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:30:03,058 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:30:03,065 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:30:03,066 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:30:03,068 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:30:03,069 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:30:03,070 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:30:04,018 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:30:04,018 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:30:04,018 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:30:04,030 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:30:04,031 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:30:04,033 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:30:32,341 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:30:32,341 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:30:32,346 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:30:32,348 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:30:32,349 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:30:32,349 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:30:33,390 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:30:33,390 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:30:33,390 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:30:33,390 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:30:33,390 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:30:33,390 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:31:21,121 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:31:21,121 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:31:21,123 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:31:21,125 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:31:21,126 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:31:21,126 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:31:22,008 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:31:22,008 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:31:22,008 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:31:22,024 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:31:22,025 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:31:22,029 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:31:33,591 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:31:33,591 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:31:33,595 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:31:33,596 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:31:33,596 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:31:33,596 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:31:35,372 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:31:35,372 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:31:35,372 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:31:35,372 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:31:35,372 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:31:35,389 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:33:31,783 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:33:31,798 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:33:31,800 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:33:31,800 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:33:31,800 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:33:31,800 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:33:33,583 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:33:33,583 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:33:33,583 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:33:33,607 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:33:33,609 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:33:33,615 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:40:43,881 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:40:43,881 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:40:43,881 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:40:43,897 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:40:43,898 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:40:43,898 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:40:45,648 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:40:45,648 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:40:45,648 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:40:45,648 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:40:45,664 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:40:45,665 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 14:45:00,267 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 14:45:00,267 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 14:45:00,267 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 14:45:00,267 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 14:45:00,267 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 14:45:00,283 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 14:45:01,836 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 14:45:01,836 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 14:45:01,836 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 14:45:01,836 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 14:45:01,851 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 14:45:01,857 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:11:33,785 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:11:33,785 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:11:33,785 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:11:33,799 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:11:33,801 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:11:33,803 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:11:37,005 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:11:37,005 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:11:37,005 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:11:37,016 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:11:37,019 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:11:53,675 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:11:53,675 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:11:53,675 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:11:53,683 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:11:53,685 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:11:53,685 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:11:56,540 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:11:56,549 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:11:56,550 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:11:56,555 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:11:56,555 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:11:56,571 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:12:19,543 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:12:19,543 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:12:19,545 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:12:19,549 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:12:19,551 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:12:19,553 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:12:22,221 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:12:22,221 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:12:22,222 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:12:22,230 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:12:22,230 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:12:22,241 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:25:16,639 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:25:16,639 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:25:16,644 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:25:16,647 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:25:16,648 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:25:16,648 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:25:19,734 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:25:19,734 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:25:19,734 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:25:19,751 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:25:19,751 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:25:19,759 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:34:38,476 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:34:38,476 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:34:38,480 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:34:38,483 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:34:38,483 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:34:38,483 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:34:39,775 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:34:39,775 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:34:39,775 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:34:39,779 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:34:39,780 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:34:39,784 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:35:40,346 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:35:40,346 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:35:40,350 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:35:40,353 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:35:40,353 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:35:40,355 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:35:41,584 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:35:41,586 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:35:41,586 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:35:41,589 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:35:41,589 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:35:41,589 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:38:50,924 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:38:50,924 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:38:50,927 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:38:50,927 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:38:50,931 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:38:50,931 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:38:52,233 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:38:52,234 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:38:52,234 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:38:52,239 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:38:52,240 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:38:52,242 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:40:37,675 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:40:37,675 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:40:37,675 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:40:37,680 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:40:37,680 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:40:37,680 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:40:38,890 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:40:38,890 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:40:38,890 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:40:38,894 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:40:38,896 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:40:38,896 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:42:59,569 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:42:59,569 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:42:59,569 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:42:59,574 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:42:59,574 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:42:59,574 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:43:00,782 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:43:00,782 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:43:00,782 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:43:00,800 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:43:00,802 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:43:00,804 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:44:53,075 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:44:53,078 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:44:53,081 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:44:53,083 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:44:53,084 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:44:53,084 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:44:54,565 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:44:54,565 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:44:54,567 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:44:54,570 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:44:54,570 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:44:54,576 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:45:30,592 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:45:30,594 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:45:30,594 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:45:30,599 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:45:30,599 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:45:30,599 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:45:31,760 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:45:31,760 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:45:31,760 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:45:31,766 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:45:31,768 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:45:31,768 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:50:19,066 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:50:19,066 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:50:19,066 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:50:19,066 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:50:19,080 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:50:19,081 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:50:20,731 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:50:20,732 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:50:20,732 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:50:20,738 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:50:20,739 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:50:20,744 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:51:37,861 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:51:37,861 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:51:37,861 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:51:37,861 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:51:37,861 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:51:37,861 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:51:39,418 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:51:39,418 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:51:39,418 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:51:39,423 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:51:39,423 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:51:39,423 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:52:13,161 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:52:13,161 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:52:13,161 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:52:13,161 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:52:13,164 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:52:13,165 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:52:14,138 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:52:14,138 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:52:14,138 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:52:14,150 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:52:14,151 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:52:14,153 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:52:28,606 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:52:28,608 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:52:28,610 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:52:28,612 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:52:28,613 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:52:28,613 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:52:30,172 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:52:30,172 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:52:30,172 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:52:30,189 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:52:30,189 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:52:30,189 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:54:10,838 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:54:10,838 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:54:10,840 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:54:10,842 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:54:10,844 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:54:10,844 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:54:11,785 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:54:11,785 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:54:11,785 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:54:11,792 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:54:11,792 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:54:11,795 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:54:38,716 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:54:38,716 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:54:38,717 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:54:38,719 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:54:38,719 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:54:38,719 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:54:39,642 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:54:39,642 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:54:39,643 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:54:39,645 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:54:39,646 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:54:39,649 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:56:38,541 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:56:38,541 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:56:38,541 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:56:38,541 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:56:38,556 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:56:38,557 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:56:40,006 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:56:40,006 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:56:40,007 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:56:40,012 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:56:40,012 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:56:40,012 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:58:03,497 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:58:03,497 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:58:03,499 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:58:03,503 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:58:03,503 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:58:03,504 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:58:04,548 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:58:04,548 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:58:04,548 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:58:04,560 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:58:04,560 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:58:04,562 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:58:10,849 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:58:10,849 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:58:10,849 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:58:10,863 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:58:10,864 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:58:10,865 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:58:11,790 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:58:11,790 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:58:11,790 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:58:11,795 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:58:11,796 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:58:11,799 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:58:39,517 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:58:39,517 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:58:39,533 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:58:39,534 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:58:39,536 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:58:39,537 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:58:41,148 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:58:41,148 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:58:41,148 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:58:41,148 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:58:41,148 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:58:41,164 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 15:58:56,866 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 15:58:56,867 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 15:58:56,869 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 15:58:56,872 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 15:58:56,873 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 15:58:56,874 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 15:58:57,833 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 15:58:57,833 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 15:58:57,833 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 15:58:57,844 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 15:58:57,844 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 15:58:57,846 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:00:29,474 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:00:29,474 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:00:29,474 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:00:29,474 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:00:29,474 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:00:29,474 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:00:31,378 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:00:31,378 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:00:31,378 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:00:31,392 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:00:31,393 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:00:31,395 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:00:46,491 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:00:46,491 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:00:46,493 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:00:46,494 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:00:46,496 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:00:46,496 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:00:47,458 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:00:47,458 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:00:47,458 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:00:47,465 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:00:47,467 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:00:47,468 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:01:01,008 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:01:01,009 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:01:01,010 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:01:01,012 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:01:01,013 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:01:01,014 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:01:01,955 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:01:01,955 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:01:01,955 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:01:01,971 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:01:01,972 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:01:01,975 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:01:57,703 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:01:57,703 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:01:57,703 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:01:57,703 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:01:57,703 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:01:57,703 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:01:59,231 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:01:59,231 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:01:59,231 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:01:59,231 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:01:59,231 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:01:59,246 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:02:33,615 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:02:33,616 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:02:33,617 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:02:33,620 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:02:33,622 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:02:33,622 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:02:34,630 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:02:34,630 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:02:34,630 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:02:34,647 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:02:34,648 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:02:34,651 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:05:33,003 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:05:33,003 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:05:33,005 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:05:33,007 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:05:33,009 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:05:33,009 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:05:34,528 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:05:34,529 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:05:34,529 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:05:34,535 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:05:34,535 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:05:34,536 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:06:16,855 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:06:16,855 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:06:16,865 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:06:16,866 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:06:16,868 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:06:16,869 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:06:17,852 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:06:17,852 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:06:17,852 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:06:17,868 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:06:17,868 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:06:17,871 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:08:04,004 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:08:04,004 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:08:04,007 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:08:04,008 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:08:04,010 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:08:04,011 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:08:04,934 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:08:04,934 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:08:04,934 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:08:04,938 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:08:04,940 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:08:04,943 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:09:26,216 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:09:26,216 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:09:26,218 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:09:26,220 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:09:26,220 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:09:26,220 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:09:27,070 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:09:27,070 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:09:27,070 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:09:27,074 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:09:27,076 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:09:27,078 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:13:26,480 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:13:26,481 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:13:26,481 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:13:26,484 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:13:26,486 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:13:26,486 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:13:27,435 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:13:27,435 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:13:27,435 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:13:27,438 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:13:27,439 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:13:27,440 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:15:53,607 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:15:53,607 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:15:53,609 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:15:53,611 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:15:53,612 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:15:53,612 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:15:55,258 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:15:55,260 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:15:55,260 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:15:55,264 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:15:55,264 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:15:55,264 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:18:32,264 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:18:32,264 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:18:32,268 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:18:32,271 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:18:32,272 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:18:32,273 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:18:33,694 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:18:33,694 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:18:33,694 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:18:33,697 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:18:33,698 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:18:33,703 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:18:43,072 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:18:43,072 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:18:43,074 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:18:43,077 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:18:43,079 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:18:43,079 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:18:44,754 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:18:44,754 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:18:44,754 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:18:44,754 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:18:44,754 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:18:44,763 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:23:53,342 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:23:53,342 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:23:53,345 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:23:53,349 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:23:53,350 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:23:53,351 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:23:54,658 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:23:54,660 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:23:54,660 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:23:54,665 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:23:54,665 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:23:54,665 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:26:28,415 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:26:28,416 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:26:28,417 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:26:28,419 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:26:28,420 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:26:28,422 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:26:29,343 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:26:29,343 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:26:29,343 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:26:29,353 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:26:29,354 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:26:29,356 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:28:58,099 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:28:58,099 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:28:58,099 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:28:58,099 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:28:58,099 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:28:58,099 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:28:59,891 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:28:59,891 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:28:59,891 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:28:59,891 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:28:59,891 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:28:59,902 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:29:25,344 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:29:25,344 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:29:25,345 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:29:25,348 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:29:25,349 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:29:25,350 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:29:26,277 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:29:26,277 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:29:26,277 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:29:26,294 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:29:26,295 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:29:26,297 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:29:56,672 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:29:56,672 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:29:56,674 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:29:56,675 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:29:56,675 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:29:56,676 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:29:57,620 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:29:57,620 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:29:57,620 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:29:57,634 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:29:57,636 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:29:57,636 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:32:57,591 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:32:57,591 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:32:57,607 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:32:57,607 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:32:57,607 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:32:57,607 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:32:59,277 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:32:59,277 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:32:59,281 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:32:59,281 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:32:59,281 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:32:59,281 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:34:40,603 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:34:40,603 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:34:40,604 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:34:40,606 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:34:40,607 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:34:40,607 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:34:41,575 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:34:41,575 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:34:41,575 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:34:41,586 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:34:41,587 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:34:41,588 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:36:04,084 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:36:04,085 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:36:04,085 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:36:04,088 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:36:04,089 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:36:04,090 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:36:05,048 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:36:05,048 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:36:05,048 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:36:05,052 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:36:05,052 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:36:05,054 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:38:31,419 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:38:31,420 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:38:31,421 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:38:31,423 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:38:31,424 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:38:31,425 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:38:32,317 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:38:32,317 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:38:32,317 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:38:32,320 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:38:32,321 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:38:32,324 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:40:00,152 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:40:00,154 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:40:00,154 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:40:00,156 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:40:00,156 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:40:00,156 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:40:01,844 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:40:01,844 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:40:01,844 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:40:01,844 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:40:01,844 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:40:01,860 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:40:26,104 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:40:26,104 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:40:26,104 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:40:26,104 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:40:26,104 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:40:26,110 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:40:27,061 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:40:27,061 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:40:27,061 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:40:27,071 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:40:27,072 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:40:27,073 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:41:12,494 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:41:12,495 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:41:12,496 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:41:12,497 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:41:12,498 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:41:12,499 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:41:13,469 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:41:13,469 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:41:13,469 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:41:13,476 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:41:13,477 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:41:13,481 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:41:33,878 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:41:33,878 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:41:33,879 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:41:33,880 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:41:33,881 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:41:33,882 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:41:34,829 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:41:34,829 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:41:34,829 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:41:34,836 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:41:34,837 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:41:34,840 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-10 16:41:48,318 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-10 16:41:48,319 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-10 16:41:48,320 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-10 16:41:48,322 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-10 16:41:48,324 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-10 16:41:48,324 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-10 16:41:49,221 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-10 16:41:49,221 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-10 16:41:49,221 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-10 16:41:49,233 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-10 16:41:49,234 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-10 16:41:49,236 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:12:36,770 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 11:12:36,770 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 11:12:36,770 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 11:12:36,785 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 11:12:36,786 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 11:12:36,787 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 11:12:37,775 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 11:12:37,776 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 11:12:37,776 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 11:12:37,780 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 11:12:37,781 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 11:12:37,783 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:16:37,930 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 11:16:37,930 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 11:16:37,932 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 11:16:37,934 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 11:16:37,937 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 11:16:37,938 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 11:16:39,403 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 11:16:39,403 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 11:16:39,403 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 11:16:39,403 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 11:16:39,403 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 11:16:39,403 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:17:51,332 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 11:17:51,332 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 11:17:51,332 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 11:17:51,343 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 11:17:51,343 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 11:17:51,344 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 11:17:52,309 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 11:17:52,309 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 11:17:52,309 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 11:17:52,314 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 11:17:52,315 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 11:17:52,316 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:28:53,214 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 11:28:53,215 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 11:28:53,216 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 11:28:53,217 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 11:28:53,219 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 11:28:53,220 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 11:28:54,121 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 11:28:54,137 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 11:28:54,139 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 11:28:54,142 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 11:28:54,144 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 11:28:54,145 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:30:15,494 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 11:30:15,494 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 11:30:15,496 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 11:30:15,506 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 11:30:15,514 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 11:30:15,515 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 11:30:16,857 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 11:30:16,857 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 11:30:16,859 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 11:30:16,864 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 11:30:16,866 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 11:30:16,869 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:59:02,700 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 11:59:02,701 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 11:59:02,702 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 11:59:02,702 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 11:59:02,704 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 11:59:02,705 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 11:59:03,871 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 11:59:03,871 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 11:59:03,872 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 11:59:03,876 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 11:59:03,877 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 11:59:03,878 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 11:59:03,903 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 11:59:03,910 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:02:32,815 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:02:32,815 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:02:32,817 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:02:32,821 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:02:32,823 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:02:32,824 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:02:34,504 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:02:34,504 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:02:34,504 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:02:34,521 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:02:34,521 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:02:34,521 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:02:34,567 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:02:34,573 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:09:42,233 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:09:42,234 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:09:42,236 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:09:42,239 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:09:42,240 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:09:42,241 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:09:43,950 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:09:43,950 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:09:43,965 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:09:43,965 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:09:43,965 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:09:43,965 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:09:44,005 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:09:44,005 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:17:38,245 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:17:38,245 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:17:38,245 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:17:38,245 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:17:38,254 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:17:38,254 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:17:39,719 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:17:39,719 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:17:39,719 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:17:39,736 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:17:39,736 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:17:39,736 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:17:39,767 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:17:39,783 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:20:22,815 - INFO - power_cost_display - power_cost_display.py:39 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:20:22,815 - INFO - power_cost_display - power_cost_display.py:44 - Interactive plot setting: True
2025-07-11 12:20:22,816 - INFO - power_cost_display - power_cost_display.py:69 - Banking option selected: Without Banking
2025-07-11 12:20:22,817 - INFO - power_cost_display - power_cost_display.py:91 - View level selected: Plant Level
2025-07-11 12:20:22,819 - INFO - power_cost_display - power_cost_display.py:115 - Grid rate set to: 4.0
2025-07-11 12:20:22,820 - INFO - power_cost_display - power_cost_display.py:133 - Renewable rate set to: 2.0
2025-07-11 12:20:22,821 - INFO - power_cost_display - power_cost_display.py:159 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:20:23,742 - INFO - power_cost_display - power_cost_display.py:167 - Successfully fetched data with 6 records
2025-07-11 12:20:23,742 - INFO - power_cost_display - power_cost_display.py:381 - Processing 'Without Banking' analysis for Plant Level view
2025-07-11 12:20:23,758 - INFO - power_cost_display - power_cost_display.py:388 - Calculating plant-level power costs without banking
2025-07-11 12:20:23,761 - INFO - power_cost_display - power_cost_display.py:396 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:20:23,762 - INFO - power_cost_display - power_cost_display.py:404 - Successfully generated summary table for without banking analysis
2025-07-11 12:20:23,764 - INFO - power_cost_display - power_cost_display.py:480 - Successfully displayed without banking analysis metrics
2025-07-11 12:20:23,781 - INFO - power_cost_display - power_cost_display.py:511 - Successfully displayed interactive without banking cost chart
2025-07-11 12:20:23,784 - INFO - power_cost_display - power_cost_display.py:576 - Successfully displayed detailed without banking cost table
2025-07-11 12:21:52,715 - INFO - power_cost_display - power_cost_display.py:39 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:21:52,717 - INFO - power_cost_display - power_cost_display.py:44 - Interactive plot setting: True
2025-07-11 12:21:52,718 - INFO - power_cost_display - power_cost_display.py:69 - Banking option selected: Without Banking
2025-07-11 12:21:52,719 - INFO - power_cost_display - power_cost_display.py:91 - View level selected: Unit-wise
2025-07-11 12:21:52,720 - INFO - power_cost_display - power_cost_display.py:115 - Grid rate set to: 4.0
2025-07-11 12:21:52,721 - INFO - power_cost_display - power_cost_display.py:133 - Renewable rate set to: 2.0
2025-07-11 12:21:52,722 - INFO - power_cost_display - power_cost_display.py:156 - Fetching unit-wise banking settlement data for plant: Kids Clinic India Limited
2025-07-11 12:21:52,964 - INFO - power_cost_display - power_cost_display.py:167 - Successfully fetched data with 250 records
2025-07-11 12:21:52,965 - INFO - power_cost_display - power_cost_display.py:381 - Processing 'Without Banking' analysis for Unit-wise view
2025-07-11 12:21:52,965 - INFO - power_cost_display - power_cost_display.py:385 - Calculating unit-wise power costs without banking
2025-07-11 12:21:52,975 - INFO - power_cost_display - power_cost_display.py:396 - Successfully calculated power costs without banking, 66 records
2025-07-11 12:21:52,979 - INFO - power_cost_display - power_cost_display.py:404 - Successfully generated summary table for without banking analysis
2025-07-11 12:21:52,981 - INFO - power_cost_display - power_cost_display.py:480 - Successfully displayed without banking analysis metrics
2025-07-11 12:21:53,007 - INFO - power_cost_display - power_cost_display.py:493 - Successfully displayed interactive unit-wise without banking cost chart
2025-07-11 12:21:53,013 - INFO - power_cost_display - power_cost_display.py:576 - Successfully displayed detailed without banking cost table
2025-07-11 12:22:11,068 - INFO - power_cost_display - power_cost_display.py:39 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:22:11,068 - INFO - power_cost_display - power_cost_display.py:44 - Interactive plot setting: True
2025-07-11 12:22:11,070 - INFO - power_cost_display - power_cost_display.py:69 - Banking option selected: Without Banking
2025-07-11 12:22:11,071 - INFO - power_cost_display - power_cost_display.py:91 - View level selected: Plant Level
2025-07-11 12:22:11,072 - INFO - power_cost_display - power_cost_display.py:115 - Grid rate set to: 4.0
2025-07-11 12:22:11,073 - INFO - power_cost_display - power_cost_display.py:133 - Renewable rate set to: 2.0
2025-07-11 12:22:11,074 - INFO - power_cost_display - power_cost_display.py:159 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:22:11,990 - INFO - power_cost_display - power_cost_display.py:167 - Successfully fetched data with 6 records
2025-07-11 12:22:11,990 - INFO - power_cost_display - power_cost_display.py:381 - Processing 'Without Banking' analysis for Plant Level view
2025-07-11 12:22:11,990 - INFO - power_cost_display - power_cost_display.py:388 - Calculating plant-level power costs without banking
2025-07-11 12:22:12,004 - INFO - power_cost_display - power_cost_display.py:396 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:22:12,005 - INFO - power_cost_display - power_cost_display.py:404 - Successfully generated summary table for without banking analysis
2025-07-11 12:22:12,009 - INFO - power_cost_display - power_cost_display.py:480 - Successfully displayed without banking analysis metrics
2025-07-11 12:22:12,027 - INFO - power_cost_display - power_cost_display.py:511 - Successfully displayed interactive without banking cost chart
2025-07-11 12:22:12,029 - INFO - power_cost_display - power_cost_display.py:576 - Successfully displayed detailed without banking cost table
2025-07-11 12:25:15,237 - INFO - power_cost_display - power_cost_display.py:39 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:25:15,237 - INFO - power_cost_display - power_cost_display.py:44 - Interactive plot setting: True
2025-07-11 12:25:15,240 - INFO - power_cost_display - power_cost_display.py:69 - Banking option selected: Without Banking
2025-07-11 12:25:15,241 - INFO - power_cost_display - power_cost_display.py:91 - View level selected: Plant Level
2025-07-11 12:25:15,244 - INFO - power_cost_display - power_cost_display.py:175 - Grid rate set to: 4.0
2025-07-11 12:25:15,245 - INFO - power_cost_display - power_cost_display.py:193 - Renewable rate set to: 2.0
2025-07-11 12:25:15,246 - INFO - power_cost_display - power_cost_display.py:224 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:25:16,678 - INFO - power_cost_display - power_cost_display.py:232 - Successfully fetched data with 6 records
2025-07-11 12:25:16,678 - INFO - power_cost_display - power_cost_display.py:446 - Processing 'Without Banking' analysis for Plant Level view
2025-07-11 12:25:16,679 - INFO - power_cost_display - power_cost_display.py:453 - Calculating plant-level power costs without banking
2025-07-11 12:25:16,686 - INFO - power_cost_display - power_cost_display.py:461 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:25:16,686 - INFO - power_cost_display - power_cost_display.py:469 - Successfully generated summary table for without banking analysis
2025-07-11 12:25:16,686 - INFO - power_cost_display - power_cost_display.py:545 - Successfully displayed without banking analysis metrics
2025-07-11 12:25:16,719 - INFO - power_cost_display - power_cost_display.py:576 - Successfully displayed interactive without banking cost chart
2025-07-11 12:25:16,719 - INFO - power_cost_display - power_cost_display.py:641 - Successfully displayed detailed without banking cost table
2025-07-11 12:28:46,946 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:28:46,946 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:28:46,948 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:28:46,950 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:28:46,950 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:28:46,952 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:28:47,826 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:28:47,826 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:28:47,826 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:28:47,831 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:28:47,831 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:28:47,834 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:28:47,854 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:28:47,857 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:37:22,007 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:37:22,007 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:37:22,008 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:37:22,009 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:37:22,010 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:37:22,011 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:37:23,022 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:37:23,022 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:37:23,022 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:37:23,026 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:37:23,026 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:37:23,028 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:37:23,053 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:37:23,057 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:41:40,675 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:41:40,675 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:41:40,675 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:41:40,675 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:41:40,675 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:41:40,675 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:41:42,579 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:41:42,579 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:41:42,580 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:41:42,585 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:41:42,587 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:41:42,592 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:41:42,633 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:41:42,638 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:42:27,040 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:42:27,040 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:42:27,040 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:42:27,045 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:42:27,047 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:42:27,048 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:42:28,594 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:42:28,594 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:42:28,594 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:42:28,608 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:42:28,608 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:42:28,608 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:42:28,640 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:42:28,640 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:47:30,101 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:47:30,102 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:47:30,104 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:47:30,105 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:47:30,107 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:47:30,107 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:47:31,085 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:47:31,086 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:47:31,086 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:47:31,090 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:47:31,091 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:47:31,093 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:47:31,122 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:47:31,125 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:49:14,163 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:49:14,163 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:49:14,164 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:49:14,166 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:49:14,168 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:49:14,169 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:49:15,767 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:49:15,770 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:49:15,772 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:49:15,776 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:49:15,778 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:49:15,781 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:49:15,808 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:49:15,814 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
2025-07-11 12:51:38,591 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-11 12:51:38,591 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-11 12:51:38,591 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-11 12:51:38,591 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-11 12:51:38,591 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-11 12:51:38,596 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-11 12:51:39,669 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-11 12:51:39,669 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-11 12:51:39,669 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-11 12:51:39,677 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-11 12:51:39,678 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-11 12:51:39,681 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-11 12:51:39,699 - INFO - power_cost_display - power_cost_display.py:320 - Successfully displayed interactive without banking cost chart
2025-07-11 12:51:39,703 - INFO - power_cost_display - power_cost_display.py:351 - Successfully displayed detailed without banking cost table
