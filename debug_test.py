#!/usr/bin/env python
import sys
import traceback

def test_imports():
    try:
        print("Testing db.safe_db_utils...")
        from db.safe_db_utils import safe_read_sql
        print("✅ safe_db_utils imported successfully")
        
        print("Testing db.fetch_summary_data...")
        from db.fetch_summary_data import fetch_generation_consumption_data
        print("✅ fetch_summary_data imported successfully")
        
        print("Testing frontend.display_plots.summary_display...")
        from frontend.display_plots.summary_display import display_generation_vs_consumption
        print("✅ summary_display imported successfully")
        
        print("All imports successful!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nFull traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    test_imports()